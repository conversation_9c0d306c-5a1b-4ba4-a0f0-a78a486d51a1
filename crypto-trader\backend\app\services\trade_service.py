"""
交易服务 - 重构后的交易协调器
职责：协调各个专门服务，执行交易流程
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from decimal import Decimal
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.db.models import Trade, Order, Position, Setting, SystemStatus, AccountMetric, APICredential
from app.services.binance_client import binance_service
from app.services.exchange_info import exchange_info_service, leverage_manager
from app.services.time_sync import time_sync_service, time_validator
from app.services.rate_limiter import with_retry, rate_limiter
from app.services.price_service import price_service

# 导入新的专门服务
from app.services.credential_service import credential_service
from app.services.risk_control_service import risk_control_service
from app.services.position_calculator_service import position_calculator_service
from app.services.position_manager_service import position_manager_service

from app.core.constants import TradingConstants, get_default_price
from app.core.exceptions import TradingException, RiskControlException, ConfigurationException

logger = logging.getLogger(__name__)

class TradeService:
    """交易服务协调器类 - 重构后职责更加清晰"""

    def __init__(self):
        """初始化交易服务协调器"""
        logger.info("交易服务协调器初始化完成")
    
    async def get_trading_mode(self, db: Session) -> str:
        """获取当前交易模式 - 委托给凭证服务"""
        return await credential_service.get_trading_mode(db)

    async def get_api_credentials(self, db: Session) -> tuple[Optional[str], Optional[str]]:
        """获取API凭证 - 委托给凭证服务"""
        return await credential_service.get_api_credentials(db)

    async def check_risk_limits(self, db: Session, symbol: str, side: str, quantity: float) -> tuple[bool, str]:
        """检查风控限制 - 委托给风控服务"""
        try:
            return await risk_control_service.comprehensive_risk_check(
                db=db,
                symbol=symbol,
                side=side,
                quantity=quantity
            )
        except RiskControlException as e:
            logger.error(f"风控检查异常: {e}")
            return False, e.message
        except Exception as e:
            logger.error(f"风控检查失败: {e}")
            return False, f"风控检查失败: {e}"
    
    async def handle_signal(self, symbol: str, signal: int, confidence: float, 
                           strategy_name: str, db: Session, strategy_id: Optional[int] = None) -> Dict[str, Any]:
        """处理交易信号"""
        try:
            # 获取交易模式
            mode = await self.get_trading_mode(db)
            
            # 确定交易方向和数量
            if signal == 1:  # 买入信号
                side = "BUY"
                quantity = await self.calculate_position_size(symbol, confidence, db, strategy_id)
            elif signal == -1:  # 卖出信号
                side = "SELL"
                quantity = await self.calculate_position_size(symbol, confidence, db, strategy_id)
            else:  # 持有信号
                return {"status": "hold", "message": "持有信号，无需交易"}
            
            # 风控检查
            risk_ok, risk_msg = await self.check_risk_limits(db, symbol, side, quantity)
            if not risk_ok:
                logger.warning(f"Risk check failed: {risk_msg}")
                return {"status": "blocked", "message": risk_msg}
            
            # 根据模式执行交易
            if mode == "paper":
                result = await self.execute_paper_trade(symbol, side, quantity, strategy_name, db)
            elif mode in ["testnet", "live"]:
                result = await self.execute_real_trade(symbol, side, quantity, strategy_name, mode, db)
            else:
                return {"status": "error", "message": f"未知交易模式: {mode}"}
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to handle signal: {e}")
            return {"status": "error", "message": str(e)}
    
    async def calculate_position_size(self, symbol: str, confidence: float, db: Session, strategy_id: Optional[int] = None) -> float:
        """计算持仓大小 - 委托给仓位计算服务"""
        try:
            confidence_decimal = Decimal(str(confidence))
            quantity_decimal = await position_calculator_service.calculate_position_size(
                db=db,
                symbol=symbol,
                confidence=confidence_decimal,
                strategy_id=strategy_id
            )
            return float(quantity_decimal)

        except Exception as e:
            logger.error(f"仓位计算失败: {e}")
            # 返回最小数量作为兜底
            from app.core.constants import get_min_quantity
            min_qty = get_min_quantity(symbol)
            return float(min_qty)
    
    async def execute_paper_trade(self, symbol: str, side: str, quantity: float,
                                 strategy_name: str, db: Session) -> Dict[str, Any]:
        """执行模拟交易"""
        try:
            # 获取实时价格（如果失败则使用默认价格）
            try:
                current_price = await price_service.get_current_price(symbol)
                if not current_price:
                    logger.warning(f"无法获取{symbol}实时价格，使用默认价格进行模拟交易")
                    current_price = float(get_default_price(symbol))
            except Exception as e:
                logger.error(f"获取实时价格失败: {e}")
                current_price = float(get_default_price(symbol))

            # 创建模拟订单
            order = Order(
                order_id=f"PAPER_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                symbol=symbol,
                side=side,
                qty=quantity,
                price=current_price,
                status="FILLED"
            )
            db.add(order)

            # 创建模拟成交记录
            commission_rate = float(TradingConstants.DEFAULT_COMMISSION_RATE)
            trade = Trade(
                trade_id=f"TRADE_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                symbol=symbol,
                side=side,
                qty=quantity,
                price=current_price,
                commission=quantity * current_price * commission_rate
            )
            db.add(trade)

            # 更新持仓 - 委托给持仓管理服务
            await position_manager_service.update_position(db, symbol, side, quantity, current_price)

            logger.info(f"Paper trade executed: {side} {quantity} {symbol} @ {current_price}")

            return {
                "status": "success",
                "mode": "paper",
                "order_id": order.order_id,
                "trade_id": trade.trade_id,
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "price": current_price,
                "message": "模拟交易执行成功"
            }

        except Exception as e:
            db.rollback()
            logger.error(f"Paper trade failed: {e}")
            raise TradingException(
                message=f"模拟交易失败: {e}",
                symbol=symbol,
                details={"side": side, "quantity": quantity, "strategy": strategy_name}
            )
    
    @with_retry('order')
    async def execute_real_trade(self, symbol: str, side: str, quantity: float, 
                                strategy_name: str, mode: str, db: Session) -> Dict[str, Any]:
        """执行实盘交易"""
        try:
            # 时钟同步检查
            can_trade, time_msg = time_validator.validate_order_timestamp()
            if not can_trade:
                return {"status": "error", "message": f"时钟同步问题: {time_msg}"}
            
            # 获取API凭证
            api_key, secret = await self.get_api_credentials(db)
            if not api_key or not secret:
                return {"status": "error", "message": "API凭证未配置"}
            
            testnet = (mode == "testnet")
            
            # 测试连接
            connection_ok = await binance_service.test_connection(api_key, secret, testnet)
            if not connection_ok:
                return {"status": "error", "message": "Binance连接失败"}
            
            # 获取Binance客户端（确保使用相同凭证和环境）
            client = await binance_service.get_client(api_key, secret, testnet)
            if not client:
                return {"status": "error", "message": "无法获取Binance客户端"}
            
            # 获取当前价格用于计算名义价值
            try:
                ticker_resp = await asyncio.wait_for(
                    client.futures_symbol_ticker(symbol=symbol), timeout=10
                )
            except asyncio.TimeoutError:
                return {"status": "error", "message": "获取价格超时"}
            # futures_symbol_ticker 在不同版本可能返回 list 或 dict
            if isinstance(ticker_resp, list):
                ticker = ticker_resp[0]
            else:
                ticker = ticker_resp
            current_price = float(ticker.get('price', 0))
            
            # 计算实际交易数量（基于USDT价值）
            usdt_value = quantity * 100  # 假设 quantity 表示“手”，每手 100 USDT 名义价值
            raw_quantity = usdt_value / current_price

            # 尝试按照交易所规则格式化
            formatted_qty = exchange_info_service.format_quantity(symbol, raw_quantity)

            # 如果格式化结果 <=0，则采用回退逻辑：保留 6 位小数并保证 ≥ 最小步进
            if formatted_qty <= 0:
                step_size = exchange_info_service.symbol_info.get(symbol, {}).get('stepSize', 0.001) or 0.001
                formatted_qty = max(round(raw_quantity, 6), step_size)

            actual_quantity = formatted_qty
            
            # 验证订单参数
            is_valid, error_msg = exchange_info_service.validate_order(
                symbol, side, actual_quantity, current_price
            )
            # 若因缺少交易对信息而无法验证，直接跳过（Testnet 环境可能无法获取完整 exchangeInfo）
            if not is_valid and "未找到交易对" not in error_msg:
                return {"status": "error", "message": f"订单验证失败: {error_msg}"}
            
            # 自动调整杠杆
            notional_value = actual_quantity * current_price
            # 在 testnet 环境可跳过自动调杠杆，避免额外 API 调用导致延迟
            leverage_ok = True
            if mode == "live":
                try:
                    leverage_ok = await asyncio.wait_for(
                        leverage_manager.auto_adjust_leverage(
                            client, symbol, notional_value, exchange_info_service
                        ), timeout=10)
                except asyncio.TimeoutError:
                    leverage_ok = False
            if not leverage_ok:
                logger.warning(f"杠杆调整失败/跳过: {symbol}")
            
            # 检查时钟同步
            can_order, time_msg = time_validator.time_sync.can_place_order()
            if not can_order:
                return {"status": "error", "message": f"时钟同步检查失败: {time_msg}"}
            
            # 获取有效时间戳
            timestamp = time_validator.get_valid_timestamp()
            
            # 检测账户持仓模式，决定 positionSide，并判断是否 reduceOnly
            position_side = "BOTH"
            reduce_only = False
            try:
                mode_info = await asyncio.wait_for(client.futures_get_position_mode(), timeout=5)
                dual_raw = mode_info.get("dualSidePosition", False)
                # Binance 可能返回 bool / str / int
                dual_side = False
                if isinstance(dual_raw, bool):
                    dual_side = dual_raw
                else:
                    dual_side = str(dual_raw).lower() in ("true", "1", "t", "yes")
                if dual_side:
                    # 先获取当前持仓
                    positions = await binance_service.get_positions(api_key, secret, testnet)
                    long_qty = next((abs(p["position_amt"]) for p in positions if p["symbol"]==symbol and p["position_side"]=="LONG"), 0)
                    short_qty = next((abs(p["position_amt"]) for p in positions if p["symbol"]==symbol and p["position_side"]=="SHORT"), 0)
                    if side == "SELL":
                        if long_qty > 0:
                            position_side = "LONG"
                            reduce_only = True  # 平掉 LONG
                        else:
                            position_side = "SHORT"  # 开新 SHORT
                    else:  # BUY
                        if short_qty > 0:
                            position_side = "SHORT"
                            reduce_only = True  # 平掉 SHORT
                        else:
                            position_side = "LONG"  # 开新 LONG
            except Exception as e:
                logger.warning(f"获取 position mode 或持仓失败，默认 BOTH: {e}")
            
            # 创建市价单
            try:
                extra_kwargs = {"positionSide": position_side}
                order_result = await asyncio.wait_for(
                    binance_service.create_order(
                        api_key=api_key,
                        secret=secret,
                        symbol=symbol,
                        side=side,
                        order_type="MARKET",
                        quantity=actual_quantity,
                        testnet=testnet,
                        **extra_kwargs
                    ), timeout=15)
            except asyncio.TimeoutError:
                return {"status": "error", "message": "下单超时"}
            
            # 保存订单记录
            order = Order(
                order_id=str(order_result["order_id"]),
                symbol=symbol,
                side=side,
                qty=actual_quantity,
                price=order_result["price"],
                status=order_result["status"]
            )
            db.add(order)
            
            # 如果订单已成交，创建成交记录
            if order_result["status"] == "FILLED":
                trade = Trade(
                    trade_id=f"BINANCE_{order_result['order_id']}",
                    symbol=symbol,
                    side=side,
                    qty=actual_quantity,
                    price=order_result["price"],
                    commission=actual_quantity * order_result["price"] * 0.001
                )
                db.add(trade)
                
                # 更新持仓 - 委托给持仓管理服务
                await position_manager_service.update_position(db, symbol, side, actual_quantity, order_result["price"])
            
            db.commit()
            
            # 同步账户信息
            await self.sync_account_info(api_key, secret, testnet, db)
            
            logger.info(f"Real trade executed: {side} {actual_quantity} {symbol} @ {order_result['price']}")
            
            return {
                "status": "success",
                "mode": mode,
                "order_id": order_result["order_id"],
                "symbol": symbol,
                "side": side,
                "quantity": actual_quantity,
                "price": order_result["price"],
                "message": f"{'Testnet' if testnet else 'Live'}交易执行成功"
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"Real trade failed: {e}")
            return {"status": "error", "message": f"实盘交易失败: {e}"}
    
    async def update_position(self, symbol: str, side: str, quantity: float, price: float, db: Session):
        """更新持仓信息 - 委托给持仓管理服务"""
        await position_manager_service.update_position(db, symbol, side, quantity, price)
    
    async def sync_account_info(self, api_key: str, secret: str, testnet: bool, db: Session):
        """同步账户信息"""
        try:
            # 获取账户信息
            account_info = await binance_service.get_account_info(api_key, secret, testnet)

            # 更新账户指标
            account_metric = AccountMetric(
                balance=account_info["available_balance"],
                equity=account_info["total_margin_balance"],
                daily_pnl=account_info["total_unrealized_pnl"],
                total_pnl=account_info["total_unrealized_pnl"]
            )
            db.add(account_metric)

            # 同步持仓信息 - 委托给持仓管理服务
            await position_manager_service.sync_positions_from_exchange(db, api_key, secret, testnet)

            logger.info("Account info synced successfully")

        except Exception as e:
            db.rollback()
            logger.error(f"Failed to sync account info: {e}")
            raise TradingException(
                message=f"账户信息同步失败: {e}",
                details={"testnet": testnet}
            )
    
    async def close_all_positions(self, db: Session) -> Dict[str, Any]:
        """平仓所有持仓 - 简化版本，委托给持仓管理服务处理本地持仓"""
        try:
            mode = await self.get_trading_mode(db)

            if mode in ["testnet", "live"]:
                api_key, secret = await self.get_api_credentials(db)
                if not api_key or not secret:
                    raise ConfigurationException("API凭证未配置", "api_credentials")

                testnet_flag = (mode == "testnet")

                # 时钟同步
                from binance.client import Client
                sync_client = Client(api_key, secret, testnet=testnet_flag)
                await time_sync_service.force_sync(sync_client)
                sync_client.close_connection()

                # 获取实时持仓
                live_positions = await binance_service.get_positions(api_key, secret, testnet_flag)
                active_positions = [p for p in live_positions if abs(float(p["position_amt"])) > 1e-8]

                if not active_positions:
                    return {"status": "success", "message": "没有需要平仓的持仓"}

                # 逐个平仓
                results = []
                for pos in active_positions:
                    symbol = pos["symbol"]
                    qty_raw = float(pos["position_amt"])
                    side = "SELL" if qty_raw > 0 else "BUY"
                    quantity = abs(qty_raw)

                    try:
                        res = await self.execute_real_trade(symbol, side, quantity, "CLOSE_ALL", mode, db)
                        results.append(res)
                    except Exception as e:
                        logger.error(f"平仓{symbol}失败: {e}")
                        results.append({"status": "error", "symbol": symbol, "message": str(e)})

                # 同步账户信息
                await self.sync_account_info(api_key, secret, testnet_flag, db)

                return {
                    "status": "success",
                    "message": f"平仓完成，共处理{len(results)}个持仓",
                    "results": results
                }
            else:
                # Paper模式 - 委托给持仓管理服务
                return await position_manager_service.close_all_positions(db)

        except Exception as e:
            logger.error(f"平仓失败: {e}")
            raise TradingException(
                message=f"平仓失败: {e}",
                details={"mode": mode if 'mode' in locals() else "unknown"}
            )

# 全局实例
trade_service = TradeService()
