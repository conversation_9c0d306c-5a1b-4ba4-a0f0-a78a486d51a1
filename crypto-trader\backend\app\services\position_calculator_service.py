"""
仓位计算服务 - 专门负责仓位大小计算
职责单一化，支持多种仓位管理策略
"""
import logging
from typing import Optional, Dict, Any
from decimal import Decimal, ROUND_DOWN
from sqlalchemy.orm import Session

from app.core.constants import RiskConstants, get_min_quantity
from app.core.exceptions import DataValidationException, ConfigurationException
from app.db.models import StrategyParam, AccountMetric
from app.services.price_service import price_service

logger = logging.getLogger(__name__)


class PositionCalculatorService:
    """仓位计算服务类"""
    
    def __init__(self):
        """初始化仓位计算服务"""
        self.default_params = RiskConstants.DEFAULT_POSITION_PARAMS
        logger.info("仓位计算服务初始化完成")
    
    async def get_available_balance(self, db: Session) -> Decimal:
        """
        获取可用余额
        
        Args:
            db: 数据库会话
            
        Returns:
            Decimal: 可用余额
        """
        try:
            account_metric = (
                db.query(AccountMetric)
                .order_by(AccountMetric.ts.desc())
                .first()
            )
            
            if account_metric:
                return Decimal(str(account_metric.equity))
            else:
                # 默认余额（用于模拟交易）
                default_balance = Decimal('10000')
                logger.warning(f"未找到账户信息，使用默认余额: {default_balance}")
                return default_balance
                
        except Exception as e:
            logger.error(f"获取可用余额失败: {e}")
            raise DataValidationException(
                message=f"获取可用余额失败: {e}",
                field="available_balance",
                details={"error": str(e)}
            )
    
    async def get_current_price(self, symbol: str) -> Decimal:
        """
        获取当前价格
        
        Args:
            symbol: 交易对
            
        Returns:
            Decimal: 当前价格
        """
        try:
            price = await price_service.get_current_price(symbol)
            if price:
                return Decimal(str(price))
            else:
                # 使用默认价格
                from app.core.constants import get_default_price
                default_price = get_default_price(symbol)
                logger.warning(f"无法获取{symbol}实时价格，使用默认价格: {default_price}")
                return default_price
                
        except Exception as e:
            logger.error(f"获取{symbol}价格失败: {e}")
            raise DataValidationException(
                message=f"获取价格失败: {e}",
                field="current_price",
                value=symbol,
                details={"symbol": symbol, "error": str(e)}
            )
    
    async def get_strategy_position_config(self, db: Session, 
                                         strategy_id: Optional[int]) -> Dict[str, Any]:
        """
        获取策略仓位配置
        
        Args:
            db: 数据库会话
            strategy_id: 策略ID
            
        Returns:
            Dict: 策略仓位配置
        """
        try:
            if not strategy_id:
                return {}
            
            strategy = db.query(StrategyParam).filter(StrategyParam.id == strategy_id).first()
            if not strategy:
                return {}
            
            return {
                'position_size': strategy.position_size,
                'position_pct': strategy.position_pct,
                'symbol': strategy.symbol
            }
            
        except Exception as e:
            logger.error(f"获取策略配置失败: {e}")
            raise ConfigurationException(
                message=f"获取策略配置失败: {e}",
                config_key="strategy_position_config",
                details={"strategy_id": strategy_id, "error": str(e)}
            )
    
    async def calculate_fixed_amount_position(self, symbol: str, 
                                            fixed_amount: Decimal) -> Decimal:
        """
        计算固定金额仓位
        
        Args:
            symbol: 交易对
            fixed_amount: 固定金额（USDT）
            
        Returns:
            Decimal: 计算出的数量
        """
        try:
            current_price = await self.get_current_price(symbol)
            quantity = fixed_amount / current_price
            
            # 应用最小数量限制
            min_qty = get_min_quantity(symbol)
            if quantity < min_qty:
                logger.warning(f"计算数量{quantity}小于最小数量{min_qty}，调整为最小数量")
                quantity = min_qty
            
            # 保留6位小数
            quantity = quantity.quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
            
            logger.info(f"固定金额仓位计算: {fixed_amount} USDT -> {quantity} {symbol} @ {current_price}")
            return quantity
            
        except Exception as e:
            logger.error(f"固定金额仓位计算失败: {e}")
            raise DataValidationException(
                message=f"固定金额仓位计算失败: {e}",
                field="fixed_amount_position",
                value=fixed_amount,
                details={"symbol": symbol, "fixed_amount": str(fixed_amount), "error": str(e)}
            )
    
    async def calculate_percentage_position(self, db: Session, symbol: str, 
                                          position_pct: Decimal, 
                                          confidence: Decimal = Decimal('100')) -> Decimal:
        """
        计算百分比仓位
        
        Args:
            db: 数据库会话
            symbol: 交易对
            position_pct: 仓位百分比 (0-100)
            confidence: 信心度 (0-100)
            
        Returns:
            Decimal: 计算出的数量
        """
        try:
            available_balance = await self.get_available_balance(db)
            current_price = await self.get_current_price(symbol)
            
            # 计算仓位比例
            position_ratio = position_pct / Decimal('100')
            confidence_multiplier = confidence / Decimal('100')
            final_ratio = position_ratio * confidence_multiplier
            
            # 限制最大百分比
            max_ratio = Decimal('0.5')  # 最大50%
            if final_ratio > max_ratio:
                logger.warning(f"仓位比例{final_ratio}超过最大限制{max_ratio}，调整为最大值")
                final_ratio = max_ratio
            
            # 计算金额和数量
            size_usdt = available_balance * final_ratio
            quantity = size_usdt / current_price
            
            # 应用最小数量限制
            min_qty = get_min_quantity(symbol)
            if quantity < min_qty:
                logger.warning(f"计算数量{quantity}小于最小数量{min_qty}，调整为最小数量")
                quantity = min_qty
            
            # 保留6位小数
            quantity = quantity.quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
            
            logger.info(f"百分比仓位计算: {position_pct}% × {confidence}% 信心度 = "
                       f"{size_usdt:.2f} USDT -> {quantity} {symbol}")
            return quantity
            
        except Exception as e:
            logger.error(f"百分比仓位计算失败: {e}")
            raise DataValidationException(
                message=f"百分比仓位计算失败: {e}",
                field="percentage_position",
                value=position_pct,
                details={
                    "symbol": symbol, 
                    "position_pct": str(position_pct), 
                    "confidence": str(confidence),
                    "error": str(e)
                }
            )
    
    async def calculate_risk_based_position(self, db: Session, symbol: str, 
                                          confidence: Decimal = Decimal('100')) -> Decimal:
        """
        计算基于风险的仓位（默认策略）
        
        Args:
            db: 数据库会话
            symbol: 交易对
            confidence: 信心度 (0-100)
            
        Returns:
            Decimal: 计算出的数量
        """
        try:
            available_balance = await self.get_available_balance(db)
            current_price = await self.get_current_price(symbol)
            
            # 使用默认风险比例
            risk_per_trade = self.default_params['risk_per_trade']
            confidence_multiplier = confidence / Decimal('100')
            position_ratio = risk_per_trade * confidence_multiplier
            
            # 限制最大仓位
            max_position_ratio = self.default_params['max_position_ratio']
            if position_ratio > max_position_ratio:
                logger.warning(f"仓位比例{position_ratio}超过最大限制{max_position_ratio}，调整为最大值")
                position_ratio = max_position_ratio
            
            # 计算金额和数量
            size_usdt = available_balance * position_ratio
            quantity = size_usdt / current_price
            
            # 应用最小数量限制
            min_qty = get_min_quantity(symbol)
            if quantity < min_qty:
                logger.warning(f"计算数量{quantity}小于最小数量{min_qty}，调整为最小数量")
                quantity = min_qty
            
            # 保留6位小数
            quantity = quantity.quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
            
            logger.info(f"风险仓位计算: {risk_per_trade} × {confidence}% 信心度 = "
                       f"{size_usdt:.2f} USDT -> {quantity} {symbol}")
            return quantity
            
        except Exception as e:
            logger.error(f"风险仓位计算失败: {e}")
            raise DataValidationException(
                message=f"风险仓位计算失败: {e}",
                field="risk_based_position",
                value=confidence,
                details={"symbol": symbol, "confidence": str(confidence), "error": str(e)}
            )
    
    async def calculate_position_size(self, db: Session, symbol: str, 
                                    confidence: Decimal = Decimal('100'),
                                    strategy_id: Optional[int] = None) -> Decimal:
        """
        计算仓位大小（主入口方法）
        
        Args:
            db: 数据库会话
            symbol: 交易对
            confidence: 信心度 (0-100)
            strategy_id: 策略ID
            
        Returns:
            Decimal: 计算出的数量
        """
        try:
            # 获取策略配置
            strategy_config = await self.get_strategy_position_config(db, strategy_id)
            
            # 根据配置选择计算方式
            if strategy_config.get('position_size') is not None:
                # 固定金额模式
                fixed_amount = Decimal(str(strategy_config['position_size']))
                return await self.calculate_fixed_amount_position(symbol, fixed_amount)
                
            elif strategy_config.get('position_pct') is not None:
                # 百分比模式
                position_pct = Decimal(str(strategy_config['position_pct']))
                return await self.calculate_percentage_position(db, symbol, position_pct, confidence)
                
            else:
                # 默认风险模式
                return await self.calculate_risk_based_position(db, symbol, confidence)
                
        except Exception as e:
            logger.error(f"仓位计算失败: {e}")
            # 返回最小数量作为兜底
            min_qty = get_min_quantity(symbol)
            logger.warning(f"仓位计算失败，返回最小数量: {min_qty}")
            return min_qty


# 全局实例
position_calculator_service = PositionCalculatorService()
