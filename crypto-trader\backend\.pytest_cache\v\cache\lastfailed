{"tests/test_risk_and_trade.py::test_check_risk_limits_daily_loss_block": true, "tests/test_risk_and_trade.py::test_execute_real_trade_success": true, "tests/test_api_endpoints.py::TestStrategyAPI::test_get_strategies": true, "tests/test_api_endpoints.py::TestStrategyAPI::test_create_strategy_validation": true, "tests/test_api_endpoints.py::TestAccountAPI::test_get_account_info": true, "tests/test_api_endpoints.py::TestAPIIntegration::test_cors_headers": true, "tests/test_services.py::TestBinanceService::test_get_client_caching": true, "tests/test_services.py::TestRateLimiter::test_allow_request_within_limit": true, "tests/test_services.py::TestRateLimiter::test_block_request_over_limit": true, "tests/test_services.py::TestRateLimiter::test_reset_after_time_window": true, "tests/test_services.py::TestTimeValidator::test_validate_order_timestamp_valid": true, "tests/test_services.py::TestTimeValidator::test_validate_order_timestamp_too_old": true, "tests/test_services.py::TestTimeValidator::test_validate_order_timestamp_future": true, "tests/test_services.py::TestMarketBroadcaster::test_add_remove_websocket": true, "tests/test_services.py::TestMarketBroadcaster::test_broadcast_message": true, "tests/test_services.py::TestMarketBroadcaster::test_broadcast_handles_failed_connections": true, "tests/test_services.py::TestServiceIntegration::test_cache_manager_redis_fallback": true, "tests/test_websocket.py::TestMarketBroadcaster::test_connection_lifecycle": true, "tests/test_websocket.py::TestMarketBroadcaster::test_multiple_connections": true, "tests/test_websocket.py::TestMarketBroadcaster::test_broadcast_error_handling": true, "tests/test_websocket.py::TestLogBroadcaster::test_log_message_broadcast": true, "tests/test_websocket.py::TestSignalBroadcaster::test_signal_broadcast": true, "tests/test_websocket.py::TestTradeBroadcaster::test_trade_broadcast": true, "tests/test_websocket.py::TestWebSocketIntegration::test_concurrent_websocket_connections": true, "tests/test_websocket.py::TestWebSocketIntegration::test_websocket_memory_cleanup": true}