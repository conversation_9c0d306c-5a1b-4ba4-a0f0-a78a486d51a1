"""FastAPI entry-point for crypto-trader backend.
Run with: uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
"""
from contextlib import asynccontextmanager
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.routing import APIRouter
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import os
import asyncio
from datetime import datetime

from app.core.config import settings
from app.core.logging_config import configure_logging, system_logger
from app.core.market_ws import MarketBroadcaster, stream_binance
from app.core.log_ws import LogBroadcaster, WSLogHandler
from app.core.signal_ws import SignalBroadcaster, TradeBroadcaster
from app.services.rate_limiter import cache_manager

# 导入异常处理器和中间件
from app.core.exceptions import (
    TradingSystemException,
    trading_system_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.core.middleware import (
    RequestIDMiddleware,
    LoggingMiddleware,
    SecurityHeadersMiddleware,
    HealthCheckMiddleware
)

# 配置结构化日志
configure_logging(
    log_level=os.getenv("LOG_LEVEL", "INFO"),
    json_logs=os.getenv("JSON_LOGS", "false").lower() == "true"
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    system_logger.info("正在启动应用...")
    
    # 初始化数据库
    try:
        from app.db.session import engine
        from app.db.base import Base
        from app.db import models  # 确保所有模型都被导入
        
        system_logger.info("正在创建数据库表...")
        Base.metadata.create_all(bind=engine)
        system_logger.info("数据库表创建成功")
    except Exception as e:
        system_logger.error(f"数据库初始化失败: {e}")
    
    # 初始化Redis缓存（可选）
    try:
        await cache_manager.init_redis()
        system_logger.info("Redis缓存已连接")
    except Exception as e:
        system_logger.warning(f"Redis连接失败，使用本地缓存: {e}")
    
    # 启动市场数据WebSocket服务
    try:
        from app.services.market_websocket import market_ws_manager
        await market_ws_manager.start()
        system_logger.info("市场数据WebSocket服务已启动")
    except Exception as e:
        system_logger.error(f"市场数据WebSocket服务启动失败: {e}")
    
    # 启动信号服务
    try:
        from app.services.signal_service import signal_service
        await signal_service.start()
        system_logger.info("信号服务已启动")
    except Exception as e:
        system_logger.error(f"信号服务启动失败: {e}")
    
    # 已改用 market_ws_manager 周期性 batch_ticker 推送行情，
    # 避免硬编码 Binance WebSocket 推送导致前端出现非启用交易对。
    # 如需直连 Binance 行情，可在此处动态读取启用交易对列表再启动 stream_binance。

    
    system_logger.info("应用启动完成")
    
    yield  # 应用运行期间
    
    # 关闭时执行
    system_logger.info("正在关闭应用...")
    try:
        from app.services.market_websocket import market_ws_manager
        await market_ws_manager.stop()
    except Exception as e:
        system_logger.error(f"关闭市场WebSocket服务失败: {e}")
    
    try:
        from app.services.signal_service import signal_service
        await signal_service.stop()
    except Exception as e:
        system_logger.error(f"关闭信号服务失败: {e}")
    
    system_logger.info("应用已关闭")

app = FastAPI(
    title="Crypto Trader Backend",
    version="0.1.0",
    lifespan=lifespan,
    description="智能加密货币交易系统后端API",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# 添加中间件（顺序很重要）
app.add_middleware(RequestIDMiddleware)
app.add_middleware(LoggingMiddleware)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(HealthCheckMiddleware)

# 更安全的CORS配置
allowed_origins = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "https://localhost:3000",
    # Vite dev server default ports
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "https://localhost:5173"
]

# 在生产环境中使用更严格的CORS设置
if not settings.debug:
    allowed_origins = ["https://your-production-domain.com"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["Content-Type", "Authorization", "X-Request-ID"],
)

# 添加异常处理器
app.add_exception_handler(TradingSystemException, trading_system_exception_handler)
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# Broadcaster singletons
broadcaster = MarketBroadcaster()
log_broadcaster = LogBroadcaster()

# Import global broadcasters from signal_ws
from app.core.signal_ws import signal_broadcaster, trade_broadcaster

# add handler
import logging

logging.getLogger().addHandler(WSLogHandler(log_broadcaster))

# Include routers
from app.api.v1.strategy import router as strategy_router
app.include_router(strategy_router, prefix="/api/v1/strategy", tags=["strategy"])
from app.api.v1.account import router as account_router
app.include_router(account_router, prefix="/api/v1/account", tags=["account"])
from app.api.v1.settings import router as settings_router
app.include_router(settings_router, prefix="/api/v1/settings", tags=["settings"])
from app.api.v1.backtest import router as backtest_router
app.include_router(backtest_router, prefix="/api/v1/backtest", tags=["backtest"])
from app.api.v1.system import router as system_router
app.include_router(system_router, prefix="/api/v1/system", tags=["system"])
from app.api.v1.signals import router as signals_router
app.include_router(signals_router, prefix="/api/v1/signals", tags=["signals"])
from app.api.v1.market import router as market_router
app.include_router(market_router, prefix="/api/v1/market", tags=["market"])
from app.api.v1.logs import router as logs_router
app.include_router(logs_router, prefix="/api/v1/logs", tags=["logs"])

app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__), "../static")), name="static")

@app.get("/api/v1/ping")
async def ping():
    return {"msg": "pong"}

@app.get("/health")
async def health():
    """基础健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "crypto-trader-backend"
    }

# Frontend market WS —— 使用 market_ws_manager 推送动态行情
from app.services.market_websocket import market_ws_manager

@app.websocket("/ws/market")
async def ws_market(ws: WebSocket):
    # 直接复用统一的 market_ws_manager，避免多处实现不一致
    await market_ws_manager.connect(ws)
    try:
        while True:
            await asyncio.sleep(60)
    except WebSocketDisconnect:
        await market_ws_manager.disconnect(ws)

# log websocket
@app.websocket("/ws/logs")
async def ws_logs(ws: WebSocket):
    await ws.accept()
    await log_broadcaster.add(ws)
    try:
        while True:
            await asyncio.sleep(60)
    except WebSocketDisconnect:
        await log_broadcaster.remove(ws)

# Signal websocket
@app.websocket("/ws/signal")
async def ws_signal(ws: WebSocket):
    await ws.accept()
    await signal_broadcaster.add(ws)
    try:
        while True:
            await asyncio.sleep(60)
    except WebSocketDisconnect:
        await signal_broadcaster.remove(ws)

# Trade websocket
@app.websocket("/ws/trade")
async def ws_trade(ws: WebSocket):
    await ws.accept()
    await trade_broadcaster.add(ws)
    try:
        while True:
            await asyncio.sleep(60)
    except WebSocketDisconnect:
        await trade_broadcaster.remove(ws)

# 应用生命周期管理已移至lifespan函数
