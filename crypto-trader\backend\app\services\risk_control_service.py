"""
风控服务 - 专门负责风险控制检查
职责单一化，提高代码可维护性和可测试性
"""
import logging
from datetime import datetime, timedelta
from typing import Tuple, Dict, Any, Optional
from decimal import Decimal
from sqlalchemy.orm import Session

from app.core.constants import RiskConstants
from app.core.exceptions import RiskControlException, ErrorCodes
from app.core.decorators import risk_exception_handler, performance_monitor, validate_parameters, is_valid_symbol, is_positive_number
from app.db.models import SystemStatus, AccountMetric, Position, StrategyParam
from app.services.price_service import price_service

logger = logging.getLogger(__name__)


class RiskControlService:
    """风控服务类"""
    
    def __init__(self, custom_limits: Optional[Dict[str, Any]] = None):
        """
        初始化风控服务
        
        Args:
            custom_limits: 自定义风控限制，覆盖默认值
        """
        self.risk_limits = RiskConstants.DEFAULT_RISK_LIMITS.copy()
        if custom_limits:
            self.risk_limits.update(custom_limits)
        
        logger.info(f"风控服务初始化完成，当前限制: {self.risk_limits}")
    
    async def check_system_risk_status(self, db: Session) -> Tuple[bool, str]:
        """
        检查系统风控状态
        
        Returns:
            Tuple[bool, str]: (是否允许交易, 风控消息)
        """
        try:
            status = db.query(SystemStatus).first()
            if status and status.risk_block:
                return False, f"系统风控阻止: {status.risk_message}"
            
            return True, "系统风控检查通过"
            
        except Exception as e:
            logger.error(f"系统风控状态检查失败: {e}")
            raise RiskControlException(
                message=f"系统风控状态检查失败: {e}",
                risk_type="SYSTEM_CHECK",
                details={"error": str(e)}
            )
    
    async def check_daily_loss_limit(self, db: Session) -> Tuple[bool, str]:
        """
        检查日亏损限制
        
        Returns:
            Tuple[bool, str]: (是否通过检查, 检查消息)
        """
        try:
            # 获取今日开始时间
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            # 查询今日账户指标
            today_metrics = (
                db.query(AccountMetric)
                .filter(AccountMetric.ts >= today_start)
                .order_by(AccountMetric.ts.desc())
                .first()
            )
            
            if not today_metrics:
                return True, "无今日交易记录，日亏损检查通过"
            
            daily_pnl = Decimal(str(today_metrics.daily_pnl))
            max_loss = self.risk_limits['max_daily_loss']
            
            if daily_pnl <= max_loss:
                return False, f"达到日亏损限制: {daily_pnl} <= {max_loss}"
            
            return True, f"日亏损检查通过: {daily_pnl} > {max_loss}"
            
        except Exception as e:
            logger.error(f"日亏损限制检查失败: {e}")
            raise RiskControlException(
                message=f"日亏损限制检查失败: {e}",
                risk_type="DAILY_LOSS_CHECK",
                details={"error": str(e)}
            )
    
    async def check_position_size_limit(self, db: Session, symbol: str, 
                                      new_quantity: float) -> Tuple[bool, str]:
        """
        检查持仓规模限制
        
        Args:
            db: 数据库会话
            symbol: 交易对
            new_quantity: 新增数量
            
        Returns:
            Tuple[bool, str]: (是否通过检查, 检查消息)
        """
        try:
            # 获取当前持仓
            current_position = db.query(Position).filter(Position.symbol == symbol).first()
            
            # 获取当前价格
            current_price = await price_service.get_current_price(symbol)
            if not current_price:
                logger.warning(f"无法获取{symbol}价格，使用默认价格检查")
                from app.core.constants import get_default_price
                current_price = float(get_default_price(symbol))
            
            # 计算新的持仓价值
            current_value = 0
            if current_position:
                current_value = abs(current_position.qty * current_position.entry_price)
            
            new_value = abs(new_quantity * current_price)
            total_value = current_value + new_value
            
            max_position_size = float(self.risk_limits['max_position_size'])
            
            if total_value > max_position_size:
                return False, f"超过持仓限制: {total_value:.2f} > {max_position_size}"
            
            return True, f"持仓规模检查通过: {total_value:.2f} <= {max_position_size}"
            
        except Exception as e:
            logger.error(f"持仓规模检查失败: {e}")
            raise RiskControlException(
                message=f"持仓规模检查失败: {e}",
                risk_type="POSITION_SIZE_CHECK",
                details={"symbol": symbol, "quantity": new_quantity, "error": str(e)}
            )
    
    async def check_leverage_limit(self, db: Session, symbol: str, 
                                 leverage: int) -> Tuple[bool, str]:
        """
        检查杠杆限制
        
        Args:
            db: 数据库会话
            symbol: 交易对
            leverage: 杠杆倍数
            
        Returns:
            Tuple[bool, str]: (是否通过检查, 检查消息)
        """
        try:
            max_leverage = self.risk_limits['max_leverage']
            
            if leverage > max_leverage:
                return False, f"超过杠杆限制: {leverage} > {max_leverage}"
            
            return True, f"杠杆检查通过: {leverage} <= {max_leverage}"
            
        except Exception as e:
            logger.error(f"杠杆限制检查失败: {e}")
            raise RiskControlException(
                message=f"杠杆限制检查失败: {e}",
                risk_type="LEVERAGE_CHECK",
                details={"symbol": symbol, "leverage": leverage, "error": str(e)}
            )
    
    async def check_drawdown_limit(self, db: Session) -> Tuple[bool, str]:
        """
        检查回撤限制
        
        Returns:
            Tuple[bool, str]: (是否通过检查, 检查消息)
        """
        try:
            # 获取最近的账户指标
            recent_metrics = (
                db.query(AccountMetric)
                .order_by(AccountMetric.ts.desc())
                .limit(100)  # 获取最近100条记录
                .all()
            )
            
            if len(recent_metrics) < 2:
                return True, "历史数据不足，回撤检查通过"
            
            # 计算最大回撤
            equity_values = [float(m.equity) for m in reversed(recent_metrics)]
            max_equity = max(equity_values)
            current_equity = equity_values[-1]
            
            if max_equity <= 0:
                return True, "权益数据异常，回撤检查通过"
            
            current_drawdown = (max_equity - current_equity) / max_equity
            max_drawdown = float(self.risk_limits['max_drawdown'])
            
            if current_drawdown > max_drawdown:
                return False, f"超过最大回撤限制: {current_drawdown:.2%} > {max_drawdown:.2%}"
            
            return True, f"回撤检查通过: {current_drawdown:.2%} <= {max_drawdown:.2%}"
            
        except Exception as e:
            logger.error(f"回撤限制检查失败: {e}")
            raise RiskControlException(
                message=f"回撤限制检查失败: {e}",
                risk_type="DRAWDOWN_CHECK",
                details={"error": str(e)}
            )
    
    @risk_exception_handler
    @performance_monitor
    @validate_parameters(
        symbol=is_valid_symbol,
        quantity=is_positive_number
    )
    async def comprehensive_risk_check(self, db: Session, symbol: str,
                                     side: str, quantity: float,
                                     leverage: int = 1) -> Tuple[bool, str]:
        """
        综合风控检查
        
        Args:
            db: 数据库会话
            symbol: 交易对
            side: 交易方向
            quantity: 交易数量
            leverage: 杠杆倍数
            
        Returns:
            Tuple[bool, str]: (是否通过所有检查, 检查结果消息)
        """
        try:
            # 执行所有风控检查
            checks = [
                ("系统风控", self.check_system_risk_status(db)),
                ("日亏损限制", self.check_daily_loss_limit(db)),
                ("持仓规模", self.check_position_size_limit(db, symbol, quantity)),
                ("杠杆限制", self.check_leverage_limit(db, symbol, leverage)),
                ("回撤限制", self.check_drawdown_limit(db)),
            ]
            
            failed_checks = []
            passed_checks = []
            
            for check_name, check_coro in checks:
                try:
                    passed, message = await check_coro
                    if passed:
                        passed_checks.append(f"{check_name}: {message}")
                    else:
                        failed_checks.append(f"{check_name}: {message}")
                except Exception as e:
                    failed_checks.append(f"{check_name}: 检查异常 - {e}")
            
            # 如果有任何检查失败，返回失败
            if failed_checks:
                error_msg = "; ".join(failed_checks)
                logger.warning(f"风控检查失败: {error_msg}")
                return False, error_msg
            
            # 所有检查通过
            success_msg = f"所有风控检查通过 ({len(passed_checks)}项)"
            logger.info(f"风控检查成功: {symbol} {side} {quantity}, {success_msg}")
            return True, success_msg
            
        except Exception as e:
            logger.error(f"综合风控检查异常: {e}")
            raise RiskControlException(
                message=f"综合风控检查异常: {e}",
                risk_type="COMPREHENSIVE_CHECK",
                details={
                    "symbol": symbol,
                    "side": side,
                    "quantity": quantity,
                    "leverage": leverage,
                    "error": str(e)
                }
            )
    
    def update_risk_limits(self, new_limits: Dict[str, Any]) -> None:
        """
        更新风控限制
        
        Args:
            new_limits: 新的风控限制
        """
        self.risk_limits.update(new_limits)
        logger.info(f"风控限制已更新: {new_limits}")
    
    def get_current_limits(self) -> Dict[str, Any]:
        """获取当前风控限制"""
        return self.risk_limits.copy()


# 全局实例
risk_control_service = RiskControlService()
