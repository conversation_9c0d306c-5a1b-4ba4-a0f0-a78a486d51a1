"""
凭证管理服务 - 专门负责API凭证管理
职责单一化，提高安全性和可维护性
"""
import logging
from typing import Tuple, Optional, Dict, Any
from sqlalchemy.orm import Session

from app.core.constants import validate_trading_mode
from app.core.exceptions import ConfigurationException, ErrorCodes
from app.core.security import vault_manager
from app.db.models import Setting, SystemStatus, APICredential

logger = logging.getLogger(__name__)


class CredentialService:
    """凭证管理服务类"""
    
    def __init__(self):
        """初始化凭证管理服务"""
        logger.info("凭证管理服务初始化完成")
    
    async def get_trading_mode(self, db: Session) -> str:
        """
        获取当前交易模式 (paper/testnet/live)
        
        优先级:
        1) system_status 最新记录
        2) api_credentials 表中活动凭证的 environment 字段
        3) 默认为 "paper"
        
        Args:
            db: 数据库会话
            
        Returns:
            str: 交易模式
        """
        try:
            # 1) 最新 system_status 记录
            status = (
                db.query(SystemStatus)
                .order_by(SystemStatus.last_update.desc())
                .first()
            )
            if status and status.mode:
                mode = status.mode.lower()
                if validate_trading_mode(mode):
                    return mode
                else:
                    logger.warning(f"无效的交易模式: {status.mode}，使用默认模式")

            # 2) 如果系统状态缺失，则根据活动凭证推断
            cred_env = (
                db.query(APICredential.environment)
                .filter(APICredential.is_active == True)
                .scalar()
            )
            if cred_env:
                mode = cred_env.lower()
                if validate_trading_mode(mode):
                    return mode

            # 3) 默认返回 paper
            return "paper"
            
        except Exception as e:
            logger.error(f"获取交易模式失败: {e}")
            raise ConfigurationException(
                message=f"获取交易模式失败: {e}",
                config_key="trading_mode",
                details={"error": str(e)}
            )
    
    async def get_api_credentials(self, db: Session) -> Tuple[Optional[str], Optional[str]]:
        """
        获取 Binance API 凭证
        
        优先级:
        1) 优先查询加密存储表 `api_credentials`
        2) 兼容旧逻辑，回退到 `settings` 明文字段
        
        Args:
            db: 数据库会话
            
        Returns:
            Tuple[Optional[str], Optional[str]]: (API密钥, 密钥)
        """
        try:
            # ---- 1. 加密凭证 ----
            cred = (
                db.query(APICredential)
                .filter(APICredential.is_active == True)
                .first()
            )
            if cred:
                try:
                    api_key, secret = vault_manager.retrieve_credentials(
                        cred.api_key_encrypted, cred.secret_key_encrypted
                    )
                    if api_key and secret:
                        logger.info("成功获取加密存储的API凭证")
                        return api_key, secret
                except Exception as e:
                    logger.error(f"解密API凭证失败: {e}")
                    # 继续尝试明文字段

            # ---- 2. 明文兼容 ----
            api_key_setting = db.query(Setting).filter(Setting.key == "binance_api_key").first()
            secret_setting = db.query(Setting).filter(Setting.key == "binance_secret").first()
            
            api_key = api_key_setting.value if api_key_setting else None
            secret = secret_setting.value if secret_setting else None
            
            if api_key and secret:
                logger.info("使用明文存储的API凭证")
                return api_key, secret
            
            logger.warning("未找到有效的API凭证")
            return None, None
            
        except Exception as e:
            logger.error(f"获取API凭证失败: {e}")
            raise ConfigurationException(
                message=f"获取API凭证失败: {e}",
                config_key="api_credentials",
                details={"error": str(e)}
            )
    
    async def validate_credentials(self, api_key: str, secret: str) -> bool:
        """
        验证API凭证格式
        
        Args:
            api_key: API密钥
            secret: 密钥
            
        Returns:
            bool: 是否有效
        """
        try:
            if not api_key or not secret:
                return False
            
            # 基本格式检查
            if len(api_key) < 10 or len(secret) < 10:
                return False
            
            # 检查是否包含非法字符
            import re
            if not re.match(r'^[A-Za-z0-9]+$', api_key):
                return False
            if not re.match(r'^[A-Za-z0-9]+$', secret):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"凭证验证失败: {e}")
            return False
    
    async def store_encrypted_credentials(self, db: Session, api_key: str, secret: str,
                                        environment: str = "testnet",
                                        user_id: str = "default") -> bool:
        """
        存储加密凭证
        
        Args:
            db: 数据库会话
            api_key: API密钥
            secret: 密钥
            environment: 环境 (testnet/live)
            user_id: 用户ID
            
        Returns:
            bool: 是否成功
        """
        try:
            # 验证凭证格式
            if not await self.validate_credentials(api_key, secret):
                raise ConfigurationException(
                    message="API凭证格式无效",
                    config_key="api_credentials"
                )
            
            # 加密存储
            api_key_encrypted, secret_encrypted = vault_manager.store_credentials(api_key, secret)
            
            # 生成掩码版本
            api_key_masked = api_key[:8] + "*" * (len(api_key) - 12) + api_key[-4:]
            
            # 停用旧凭证
            db.query(APICredential).filter(
                APICredential.user_id == user_id,
                APICredential.is_active == True
            ).update({"is_active": False})
            
            # 创建新凭证记录
            new_cred = APICredential(
                user_id=user_id,
                exchange="binance",
                api_key_encrypted=api_key_encrypted,
                api_key_id=vault_manager.generate_key_id(),
                secret_key_encrypted=secret_encrypted,
                secret_key_id=vault_manager.generate_key_id(),
                api_key_masked=api_key_masked,
                environment=environment,
                permissions=["futures_trade", "market_data"],
                is_active=True
            )
            
            db.add(new_cred)
            db.commit()
            
            logger.info(f"成功存储加密凭证: {api_key_masked} ({environment})")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"存储加密凭证失败: {e}")
            raise ConfigurationException(
                message=f"存储加密凭证失败: {e}",
                config_key="store_credentials",
                details={"environment": environment, "error": str(e)}
            )
    
    async def get_credential_info(self, db: Session) -> Dict[str, Any]:
        """
        获取凭证信息（不包含敏感数据）
        
        Args:
            db: 数据库会话
            
        Returns:
            Dict: 凭证信息
        """
        try:
            cred = (
                db.query(APICredential)
                .filter(APICredential.is_active == True)
                .first()
            )
            
            if cred:
                return {
                    "has_credentials": True,
                    "api_key_masked": cred.api_key_masked,
                    "environment": cred.environment,
                    "exchange": cred.exchange,
                    "permissions": cred.permissions,
                    "created_at": cred.created_at.isoformat() if cred.created_at else None,
                    "last_validated": cred.last_validated.isoformat() if cred.last_validated else None
                }
            else:
                # 检查明文凭证
                api_key_setting = db.query(Setting).filter(Setting.key == "binance_api_key").first()
                if api_key_setting and api_key_setting.value:
                    api_key = api_key_setting.value
                    api_key_masked = api_key[:8] + "*" * (len(api_key) - 12) + api_key[-4:]
                    return {
                        "has_credentials": True,
                        "api_key_masked": api_key_masked,
                        "environment": "unknown",
                        "exchange": "binance",
                        "permissions": ["unknown"],
                        "storage_type": "plaintext"
                    }
                else:
                    return {
                        "has_credentials": False,
                        "message": "未配置API凭证"
                    }
                    
        except Exception as e:
            logger.error(f"获取凭证信息失败: {e}")
            return {
                "has_credentials": False,
                "error": str(e)
            }
    
    async def delete_credentials(self, db: Session, user_id: str = "default") -> bool:
        """
        删除凭证
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            bool: 是否成功
        """
        try:
            # 停用加密凭证
            updated_count = db.query(APICredential).filter(
                APICredential.user_id == user_id,
                APICredential.is_active == True
            ).update({"is_active": False})
            
            # 删除明文凭证
            db.query(Setting).filter(Setting.key.in_(["binance_api_key", "binance_secret"])).delete()
            
            db.commit()
            
            logger.info(f"成功删除用户 {user_id} 的凭证，影响 {updated_count} 条记录")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"删除凭证失败: {e}")
            raise ConfigurationException(
                message=f"删除凭证失败: {e}",
                config_key="delete_credentials",
                details={"user_id": user_id, "error": str(e)}
            )


# 全局实例
credential_service = CredentialService()
