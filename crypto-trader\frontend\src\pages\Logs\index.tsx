import React, { useState, useEffect } from 'react';
import { Checkbox } from 'antd';
import { Card, List, Typography, Badge, Spin, Alert } from 'antd';

const { Title } = Typography;

interface LogEntry {
  id: string;
  timestamp: string;
  level: '信息' | '警告' | '错误' | '成功';
  message: string;
}

const Logs: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [tradeOnly, setTradeOnly] = useState<boolean>(true);
  const [systemOnly, setSystemOnly] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLogs = async () => {
      try {
        const res = await fetch('/api/v1/logs/recent?limit=200');
        if (!res.ok) throw new Error('服务器错误');
        const data: LogEntry[] = await res.json();
        setLogs(data);
        setError(null);
      } catch (err) {
        console.error('Failed to load logs:', err);
        setError('加载日志失败');
      } finally {
        setLoading(false);
      }
    };
    fetchLogs();



  }, []);

  const getBadgeStatus = (level: string): "success" | "processing" | "default" | "error" | "warning" => {
    switch (level) {
      case '成功': return 'success';
      case '警告': return 'warning';
      case '错误': return 'error';
      default: return 'processing';
    }
  };

  const getLogColor = (level: string) => {
    switch (level) {
      case '成功': return '#00ff88';
      case '警告': return '#ffaa00';
      case '错误': return '#ff4757';
      default: return '#00d4ff';
    }
  };

  const tradeKeywordMatch = (msg: string) => {
    const lowered = msg.toLowerCase();
    const keywords = ["trade", "order", "position", "pnl", "execute", "buy", "sell"];
    const sqlPrefixes = ["select ", "update ", "insert ", "delete ", "begin", "rollback", "cached since", "pragma "];
    if (sqlPrefixes.some(p => lowered.startsWith(p))) return false;
    return keywords.some(k => lowered.includes(k));
  };

  const systemKeywordMatch = (msg: string) => !tradeKeywordMatch(msg);

  let filteredLogs = logs;
  if (tradeOnly) {
    filteredLogs = logs.filter(l => tradeKeywordMatch(l.message));
  } else if (systemOnly) {
    filteredLogs = logs.filter(l => systemKeywordMatch(l.message));
  }

  return (
    <div style={{ padding: '0' }}>
      <Title 
        level={2} 
        style={{ 
          color: '#00d4ff', 
          marginBottom: '24px',
          fontFamily: 'Consolas',
          textTransform: 'uppercase',
          letterSpacing: '2px',
          textShadow: '0 0 10px rgba(0, 212, 255, 0.3)'
        }}
      >
        📋 系统日志
      </Title>
      
      {error && (
        <Alert 
          message="错误" 
          description={error} 
          type="error" 
          style={{ marginBottom: '16px' }}
        />
      )}
      
      <div style={{ marginBottom: 12, display: 'flex', gap: 24 }}>
        <Checkbox
          checked={tradeOnly}
          onChange={e => {
            const val = e.target.checked;
            setTradeOnly(val);
            if (val) setSystemOnly(false);
          }}
        >
          仅显示交易相关
        </Checkbox>
        <Checkbox
          checked={systemOnly}
          onChange={e => {
            const val = e.target.checked;
            setSystemOnly(val);
            if (val) setTradeOnly(false);
          }}
        >
          仅显示系统日志
        </Checkbox>
      </div>

      <Card 
        title="🔍 实时日志" 
        styles={{
          header: {
            background: '#2a2a2a',
            color: '#00d4ff',
            fontFamily: 'Consolas',
            fontWeight: 'bold',
            textTransform: 'uppercase',
            letterSpacing: '1px',
          },
          body: {
            background: '#1f1f1f',
            padding: '16px',
          },
        }}
      >
        <Spin spinning={loading}>
    <List
      dataSource={filteredLogs}
      renderItem={(item) => (
            <List.Item
              style={{
                padding: '12px 0',
                borderBottom: '1px solid #3a3a3a',
                fontFamily: 'Consolas',
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', width: '100%' }}>
                <Badge 
                  status={getBadgeStatus(item.level)} 
                  style={{ marginRight: '8px' }}
                />
                <span 
                  style={{ 
                    color: '#b0b0b0', 
                    fontSize: '12px',
                    minWidth: '140px',
                    fontFamily: 'Consolas'
                  }}
                >
                  {item.timestamp}
                </span>
                <span 
                  style={{ 
                    color: getLogColor(item.level),
                    fontWeight: 'bold',
                    minWidth: '60px',
                    textAlign: 'center',
                    fontSize: '11px',
                    padding: '2px 8px',
                    border: `1px solid ${getLogColor(item.level)}`,
                    borderRadius: '4px',
                    background: `${getLogColor(item.level)}20`,
                    fontFamily: 'Consolas'
                  }}
                >
                  {item.level}
                </span>
                <span 
                  style={{ 
                    color: '#ffffff',
                    flex: 1,
                    fontFamily: 'Consolas'
                  }}
                >
                  {item.message}
                </span>
              </div>
        </List.Item>
      )}
    />
        </Spin>
      </Card>
    </div>
  );
};

export default Logs;
