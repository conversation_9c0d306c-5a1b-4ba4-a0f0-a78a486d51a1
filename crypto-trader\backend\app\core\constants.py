"""
交易系统配置常量
统一管理所有硬编码值，提高可维护性
"""
from typing import Dict, Any
from decimal import Decimal


class TradingConstants:
    """交易相关常量"""
    
    # 默认交易参数
    DEFAULT_COMMISSION_RATE = Decimal('0.001')  # 0.1% 手续费
    DEFAULT_SLIPPAGE_RATE = Decimal('0.0001')   # 0.01% 滑点
    
    # 默认价格（用于模拟交易）
    DEFAULT_PRICES = {
        'BTCUSDT': Decimal('50000.0'),
        'ETHUSDT': Decimal('3000.0'),
        'BNBUSDT': Decimal('300.0'),
        'ADAUSDT': Decimal('0.5'),
        'SOLUSDT': Decimal('100.0'),
    }
    
    # 最小交易数量
    MIN_TRADE_QUANTITIES = {
        'BTCUSDT': Decimal('0.001'),
        'ETHUSDT': Decimal('0.001'),
        'BNBUSDT': Decimal('0.01'),
        'ADAUSDT': Decimal('1.0'),
        'SOLUSDT': Decimal('0.01'),
    }
    
    # 交易模式
    TRADING_MODES = ['paper', 'testnet', 'live']
    
    # 订单类型
    ORDER_TYPES = ['MARKET', 'LIMIT', 'STOP', 'STOP_MARKET']
    
    # 订单状态
    ORDER_STATUSES = ['NEW', 'PARTIALLY_FILLED', 'FILLED', 'CANCELED', 'REJECTED', 'EXPIRED']


class RiskConstants:
    """风控相关常量"""
    
    # 默认风控限制
    DEFAULT_RISK_LIMITS = {
        'max_daily_loss': Decimal('-1000'),      # 最大日亏损 USDT
        'max_position_size': Decimal('10000'),   # 最大持仓金额 USDT
        'max_leverage': 10,                      # 最大杠杆倍数
        'max_drawdown': Decimal('0.2'),          # 最大回撤 20%
        'position_limit_pct': Decimal('0.5'),    # 单个持仓最大占比 50%
    }
    
    # 风险等级
    RISK_LEVELS = {
        'LOW': 1,
        'MEDIUM': 2,
        'HIGH': 3,
        'CRITICAL': 4
    }
    
    # 默认仓位管理参数
    DEFAULT_POSITION_PARAMS = {
        'risk_per_trade': Decimal('0.02'),       # 每笔交易风险比例 2%
        'max_position_ratio': Decimal('0.1'),    # 最大仓位比例 10%
        'default_position_pct': Decimal('2.0'),  # 默认仓位百分比 2%
    }


class SystemConstants:
    """系统相关常量"""
    
    # 时间相关
    BAR_INTERVAL_SECONDS = 900  # 15分钟 = 900秒
    HISTORY_LIMIT = 200         # 历史数据保留条数
    
    # 重试相关
    MAX_RETRY_ATTEMPTS = 3
    BASE_RETRY_DELAY = 1.0      # 基础重试延迟（秒）
    MAX_RETRY_DELAY = 30.0      # 最大重试延迟（秒）
    RETRY_BACKOFF_FACTOR = 2.0  # 退避因子
    
    # 超时设置
    API_TIMEOUT_SECONDS = 15
    WEBSOCKET_TIMEOUT_SECONDS = 30
    DATABASE_TIMEOUT_SECONDS = 10
    
    # 缓存TTL（秒）
    CACHE_TTL = {
        'exchange_info': 3600,      # 1小时
        'symbol_info': 1800,        # 30分钟
        'account_info': 30,         # 30秒
        'market_data': 5,           # 5秒
        'price_data': 1,            # 1秒
    }
    
    # 日志相关
    LOG_LEVELS = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
    MAX_LOG_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5


class APIConstants:
    """API相关常量"""
    
    # Binance API限制
    BINANCE_RATE_LIMITS = {
        'default': {'requests': 1200, 'window': 60},    # 默认限制
        'order': {'requests': 10, 'window': 1},         # 下单限制
        'market': {'requests': 100, 'window': 60},      # 市场数据限制
        'account': {'requests': 180, 'window': 60},     # 账户信息限制
    }
    
    # API端点
    BINANCE_ENDPOINTS = {
        'testnet': {
            'base_url': 'https://testnet.binancefuture.com',
            'ws_url': 'wss://stream.binancefuture.com/ws'
        },
        'live': {
            'base_url': 'https://fapi.binance.com',
            'ws_url': 'wss://fstream.binance.com/ws'
        }
    }
    
    # HTTP状态码
    HTTP_SUCCESS_CODES = [200, 201, 202]
    HTTP_RETRY_CODES = [429, 502, 503, 504]


class ValidationConstants:
    """数据验证相关常量"""
    
    # 数值范围
    MIN_PRICE = Decimal('0.********')
    MAX_PRICE = Decimal('1000000')
    MIN_QUANTITY = Decimal('0.********')
    MAX_QUANTITY = Decimal('1000000')
    
    # 字符串长度限制
    MAX_SYMBOL_LENGTH = 20
    MAX_ORDER_ID_LENGTH = 64
    MAX_MESSAGE_LENGTH = 1024
    
    # 正则表达式
    SYMBOL_PATTERN = r'^[A-Z]{2,10}USDT$'
    ORDER_ID_PATTERN = r'^[A-Za-z0-9_-]{1,64}$'


class DatabaseConstants:
    """数据库相关常量"""
    
    # 连接池设置
    DB_POOL_SIZE = 10
    DB_MAX_OVERFLOW = 20
    DB_POOL_TIMEOUT = 30
    
    # 查询限制
    MAX_QUERY_RESULTS = 10000
    DEFAULT_PAGE_SIZE = 50
    MAX_PAGE_SIZE = 1000
    
    # 数据保留期限（天）
    DATA_RETENTION_DAYS = {
        'logs': 30,
        'trades': 365,
        'orders': 365,
        'signals': 90,
        'market_data': 7,
    }


def get_default_price(symbol: str) -> Decimal:
    """获取默认价格"""
    return TradingConstants.DEFAULT_PRICES.get(symbol, Decimal('1.0'))


def get_min_quantity(symbol: str) -> Decimal:
    """获取最小交易数量"""
    return TradingConstants.MIN_TRADE_QUANTITIES.get(symbol, Decimal('0.001'))


def get_cache_ttl(cache_type: str) -> int:
    """获取缓存TTL"""
    return SystemConstants.CACHE_TTL.get(cache_type, 300)  # 默认5分钟


def get_rate_limit(limit_type: str) -> Dict[str, int]:
    """获取速率限制配置"""
    return APIConstants.BINANCE_RATE_LIMITS.get(limit_type, 
                                               APIConstants.BINANCE_RATE_LIMITS['default'])


def validate_trading_mode(mode: str) -> bool:
    """验证交易模式"""
    return mode in TradingConstants.TRADING_MODES


def validate_order_type(order_type: str) -> bool:
    """验证订单类型"""
    return order_type in TradingConstants.ORDER_TYPES


def validate_symbol_format(symbol: str) -> bool:
    """验证交易对格式"""
    import re
    return bool(re.match(ValidationConstants.SYMBOL_PATTERN, symbol))


# 导出常用常量
__all__ = [
    'TradingConstants',
    'RiskConstants', 
    'SystemConstants',
    'APIConstants',
    'ValidationConstants',
    'DatabaseConstants',
    'get_default_price',
    'get_min_quantity',
    'get_cache_ttl',
    'get_rate_limit',
    'validate_trading_mode',
    'validate_order_type',
    'validate_symbol_format',
]
