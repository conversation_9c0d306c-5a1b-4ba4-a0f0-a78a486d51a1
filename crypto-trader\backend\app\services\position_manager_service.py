"""
持仓管理服务 - 专门负责持仓更新和管理
职责单一化，提高数据一致性和可维护性
"""
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from sqlalchemy.orm import Session

from app.core.exceptions import TradingException, DataValidationException
from app.db.models import Position, AccountMetric
from app.services.binance_client import binance_service

logger = logging.getLogger(__name__)


class PositionManagerService:
    """持仓管理服务类"""
    
    def __init__(self):
        """初始化持仓管理服务"""
        logger.info("持仓管理服务初始化完成")
    
    async def update_position(self, db: Session, symbol: str, side: str, 
                            quantity: float, price: float) -> None:
        """
        更新持仓信息
        
        Args:
            db: 数据库会话
            symbol: 交易对
            side: 交易方向 (BUY/SELL)
            quantity: 交易数量
            price: 交易价格
        """
        try:
            # 使用 SELECT ... FOR UPDATE 锁定行，避免并发写冲突
            position = (
                db.query(Position)
                .with_for_update()
                .filter(Position.symbol == symbol)
                .first()
            )
            
            if not position:
                # 创建新持仓
                new_qty = quantity if side == "BUY" else -quantity
                position = Position(
                    symbol=symbol,
                    qty=new_qty,
                    entry_price=price,
                    unrealized_pnl=0
                )
                db.add(position)
                logger.info(f"创建新持仓: {symbol} {new_qty} @ {price}")
            else:
                # 更新现有持仓
                if side == "BUY":
                    new_qty = position.qty + quantity
                else:
                    new_qty = position.qty - quantity
                
                if abs(new_qty) < 1e-8:  # 接近0，视为平仓
                    db.delete(position)
                    logger.info(f"平仓: {symbol}")
                else:
                    # 更新持仓
                    old_qty = position.qty
                    
                    # 判断是否为同方向加仓
                    if (old_qty > 0 and new_qty > 0) or (old_qty < 0 and new_qty < 0):
                        # 同方向加仓，更新平均价格
                        total_value = old_qty * position.entry_price + quantity * price * (1 if side == "BUY" else -1)
                        position.entry_price = abs(total_value / new_qty)
                        logger.info(f"同方向加仓: {symbol} {old_qty} -> {new_qty} @ {position.entry_price}")
                    else:
                        # 反向交易，保持原入场价格
                        logger.info(f"反向交易: {symbol} {old_qty} -> {new_qty} @ {position.entry_price}")
                    
                    position.qty = new_qty
            
            db.commit()
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新持仓失败: {e}")
            raise TradingException(
                message=f"更新持仓失败: {e}",
                symbol=symbol,
                details={
                    "side": side,
                    "quantity": quantity,
                    "price": price,
                    "error": str(e)
                }
            )
    
    async def sync_positions_from_exchange(self, db: Session, api_key: str, 
                                         secret: str, testnet: bool) -> Dict[str, Any]:
        """
        从交易所同步持仓信息
        
        Args:
            db: 数据库会话
            api_key: API密钥
            secret: 密钥
            testnet: 是否测试网
            
        Returns:
            Dict: 同步结果
        """
        try:
            # 获取交易所持仓信息
            positions = await binance_service.get_positions(api_key, secret, testnet)
            
            # 清除旧持仓记录
            db.query(Position).delete()
            
            # 添加新持仓记录
            active_positions = 0
            total_unrealized_pnl = 0
            
            for pos in positions:
                position_amt = float(pos["position_amt"])
                if abs(position_amt) > 1e-8:  # 只保存有持仓的记录
                    position = Position(
                        symbol=pos["symbol"],
                        qty=position_amt,
                        entry_price=float(pos["entry_price"]),
                        unrealized_pnl=float(pos["unrealized_pnl"])
                    )
                    db.add(position)
                    active_positions += 1
                    total_unrealized_pnl += float(pos["unrealized_pnl"])
            
            db.commit()
            
            logger.info(f"持仓同步完成: {active_positions}个活跃持仓，总未实现盈亏: {total_unrealized_pnl:.2f}")
            
            return {
                "status": "success",
                "active_positions": active_positions,
                "total_unrealized_pnl": total_unrealized_pnl,
                "message": "持仓同步成功"
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"持仓同步失败: {e}")
            raise TradingException(
                message=f"持仓同步失败: {e}",
                details={"testnet": testnet, "error": str(e)}
            )
    
    async def get_position_summary(self, db: Session) -> Dict[str, Any]:
        """
        获取持仓汇总信息
        
        Args:
            db: 数据库会话
            
        Returns:
            Dict: 持仓汇总
        """
        try:
            positions = db.query(Position).all()
            
            if not positions:
                return {
                    "total_positions": 0,
                    "total_unrealized_pnl": 0,
                    "long_positions": 0,
                    "short_positions": 0,
                    "positions": []
                }
            
            total_unrealized_pnl = 0
            long_positions = 0
            short_positions = 0
            position_list = []
            
            for pos in positions:
                total_unrealized_pnl += pos.unrealized_pnl
                
                if pos.qty > 0:
                    long_positions += 1
                    direction = "LONG"
                else:
                    short_positions += 1
                    direction = "SHORT"
                
                position_list.append({
                    "symbol": pos.symbol,
                    "quantity": pos.qty,
                    "entry_price": pos.entry_price,
                    "unrealized_pnl": pos.unrealized_pnl,
                    "direction": direction,
                    "notional_value": abs(pos.qty * pos.entry_price)
                })
            
            return {
                "total_positions": len(positions),
                "total_unrealized_pnl": total_unrealized_pnl,
                "long_positions": long_positions,
                "short_positions": short_positions,
                "positions": position_list
            }
            
        except Exception as e:
            logger.error(f"获取持仓汇总失败: {e}")
            raise DataValidationException(
                message=f"获取持仓汇总失败: {e}",
                field="position_summary",
                details={"error": str(e)}
            )
    
    async def close_position(self, db: Session, symbol: str) -> Dict[str, Any]:
        """
        平仓指定交易对
        
        Args:
            db: 数据库会话
            symbol: 交易对
            
        Returns:
            Dict: 平仓结果
        """
        try:
            position = db.query(Position).filter(Position.symbol == symbol).first()
            
            if not position:
                return {
                    "status": "no_position",
                    "message": f"未找到{symbol}的持仓"
                }
            
            if abs(position.qty) < 1e-8:
                return {
                    "status": "no_position", 
                    "message": f"{symbol}持仓数量为0"
                }
            
            # 删除持仓记录
            db.delete(position)
            db.commit()
            
            logger.info(f"平仓成功: {symbol} {position.qty}")
            
            return {
                "status": "success",
                "symbol": symbol,
                "closed_quantity": position.qty,
                "entry_price": position.entry_price,
                "unrealized_pnl": position.unrealized_pnl,
                "message": f"成功平仓 {symbol}"
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"平仓失败: {e}")
            raise TradingException(
                message=f"平仓失败: {e}",
                symbol=symbol,
                details={"error": str(e)}
            )
    
    async def close_all_positions(self, db: Session) -> Dict[str, Any]:
        """
        平仓所有持仓
        
        Args:
            db: 数据库会话
            
        Returns:
            Dict: 平仓结果
        """
        try:
            positions = db.query(Position).filter(Position.qty != 0).all()
            
            if not positions:
                return {
                    "status": "no_positions",
                    "message": "没有需要平仓的持仓"
                }
            
            closed_positions = []
            total_pnl = 0
            
            for position in positions:
                closed_positions.append({
                    "symbol": position.symbol,
                    "quantity": position.qty,
                    "entry_price": position.entry_price,
                    "unrealized_pnl": position.unrealized_pnl
                })
                total_pnl += position.unrealized_pnl
            
            # 删除所有持仓
            db.query(Position).delete()
            db.commit()
            
            logger.info(f"全部平仓成功: {len(closed_positions)}个持仓，总盈亏: {total_pnl:.2f}")
            
            return {
                "status": "success",
                "closed_count": len(closed_positions),
                "total_pnl": total_pnl,
                "closed_positions": closed_positions,
                "message": f"成功平仓 {len(closed_positions)} 个持仓"
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"全部平仓失败: {e}")
            raise TradingException(
                message=f"全部平仓失败: {e}",
                details={"error": str(e)}
            )
    
    async def update_unrealized_pnl(self, db: Session, symbol: str, current_price: float) -> None:
        """
        更新未实现盈亏
        
        Args:
            db: 数据库会话
            symbol: 交易对
            current_price: 当前价格
        """
        try:
            position = db.query(Position).filter(Position.symbol == symbol).first()
            
            if position and abs(position.qty) > 1e-8:
                # 计算未实现盈亏
                unrealized_pnl = (current_price - position.entry_price) * position.qty
                position.unrealized_pnl = unrealized_pnl
                db.commit()
                
                logger.debug(f"更新{symbol}未实现盈亏: {unrealized_pnl:.2f}")
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新未实现盈亏失败: {e}")
            # 不抛出异常，避免影响主流程


# 全局实例
position_manager_service = PositionManagerService()
