["tests/test_api_endpoints.py::TestAPIIntegration::test_api_error_handling", "tests/test_api_endpoints.py::TestAPIIntegration::test_cors_headers", "tests/test_api_endpoints.py::TestAccountAPI::test_get_account_info", "tests/test_api_endpoints.py::TestMarketAPI::test_market_status", "tests/test_api_endpoints.py::TestPerformanceAPI::test_concurrent_requests", "tests/test_api_endpoints.py::TestStrategyAPI::test_create_strategy_validation", "tests/test_api_endpoints.py::TestStrategyAPI::test_get_strategies", "tests/test_api_endpoints.py::TestSystemAPI::test_health_check", "tests/test_api_endpoints.py::TestSystemAPI::test_ping", "tests/test_core_functionality.py::TestDataValidation::test_percentage_validation", "tests/test_core_functionality.py::TestDataValidation::test_price_validation", "tests/test_core_functionality.py::TestDataValidation::test_quantity_validation", "tests/test_core_functionality.py::TestDataValidation::test_symbol_validation", "tests/test_core_functionality.py::TestErrorCodes::test_error_codes_constants", "tests/test_core_functionality.py::TestExceptionHandling::test_api_connection_exception", "tests/test_core_functionality.py::TestExceptionHandling::test_configuration_exception", "tests/test_core_functionality.py::TestExceptionHandling::test_data_validation_exception", "tests/test_core_functionality.py::TestExceptionHandling::test_risk_control_exception", "tests/test_core_functionality.py::TestExceptionHandling::test_trading_exception", "tests/test_core_functionality.py::TestExceptionHandling::test_trading_system_exception_creation", "tests/test_core_functionality.py::TestRiskCalculations::test_max_drawdown_calculation", "tests/test_core_functionality.py::TestRiskCalculations::test_position_size_calculation", "tests/test_core_functionality.py::TestRiskCalculations::test_risk_reward_ratio", "tests/test_core_functionality.py::TestSystemIntegration::test_configuration_loading", "tests/test_core_functionality.py::TestSystemIntegration::test_error_response_format", "tests/test_core_functionality.py::TestUtilityFunctions::test_calculate_percentage_change", "tests/test_core_functionality.py::TestUtilityFunctions::test_calculate_pnl", "tests/test_core_functionality.py::TestUtilityFunctions::test_format_price", "tests/test_core_functionality.py::TestUtilityFunctions::test_timestamp_conversion", "tests/test_risk_and_trade.py::test_check_risk_limits_daily_loss_block", "tests/test_risk_and_trade.py::test_check_risk_limits_pass", "tests/test_risk_and_trade.py::test_execute_real_trade_success", "tests/test_services.py::TestBinanceService::test_get_client_caching", "tests/test_services.py::TestBinanceService::test_test_connection_failure", "tests/test_services.py::TestBinanceService::test_test_connection_success", "tests/test_services.py::TestMarketBroadcaster::test_add_remove_websocket", "tests/test_services.py::TestMarketBroadcaster::test_broadcast_handles_failed_connections", "tests/test_services.py::TestMarketBroadcaster::test_broadcast_message", "tests/test_services.py::TestRateLimiter::test_allow_request_within_limit", "tests/test_services.py::TestRateLimiter::test_block_request_over_limit", "tests/test_services.py::TestRateLimiter::test_reset_after_time_window", "tests/test_services.py::TestServiceIntegration::test_cache_manager_redis_fallback", "tests/test_services.py::TestTimeValidator::test_validate_order_timestamp_future", "tests/test_services.py::TestTimeValidator::test_validate_order_timestamp_too_old", "tests/test_services.py::TestTimeValidator::test_validate_order_timestamp_valid", "tests/test_websocket.py::TestLogBroadcaster::test_log_message_broadcast", "tests/test_websocket.py::TestMarketBroadcaster::test_broadcast_error_handling", "tests/test_websocket.py::TestMarketBroadcaster::test_connection_lifecycle", "tests/test_websocket.py::TestMarketBroadcaster::test_multiple_connections", "tests/test_websocket.py::TestSignalBroadcaster::test_signal_broadcast", "tests/test_websocket.py::TestTradeBroadcaster::test_trade_broadcast", "tests/test_websocket.py::TestWebSocketEndpoints::test_logs_websocket_connection", "tests/test_websocket.py::TestWebSocketEndpoints::test_market_websocket_connection", "tests/test_websocket.py::TestWebSocketEndpoints::test_signal_websocket_connection", "tests/test_websocket.py::TestWebSocketEndpoints::test_trade_websocket_connection", "tests/test_websocket.py::TestWebSocketIntegration::test_concurrent_websocket_connections", "tests/test_websocket.py::TestWebSocketIntegration::test_websocket_memory_cleanup"]