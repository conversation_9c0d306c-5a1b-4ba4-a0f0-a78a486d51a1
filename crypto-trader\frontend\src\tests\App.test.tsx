/**
 * App组件测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import App from '../App';
import { apiService } from '../services/api';
import { wsService } from '../services/websocket';
import { MarketProvider } from '../contexts/MarketContext';

// Mock API服务
vi.mock('../services/api', () => ({
  apiService: {
    getSystemStatus: vi.fn(),
    getAccountSummary: vi.fn(),
    getStrategies: vi.fn(),
    getSystemInfo: vi.fn(),
  },
}));

// Mock WebSocket服务
vi.mock('../services/websocket', () => ({
  wsService: {
    subscribeToTrades: vi.fn(() => () => {}), // 返回取消订阅函数
    subscribeToSignals: vi.fn(() => () => {}), // 返回取消订阅函数
    subscribeToLogs: vi.fn(() => () => {}), // 返回取消订阅函数
  },
}));

// Mock Ant Design的ConfigProvider
vi.mock('antd', async () => {
  const actual = await vi.importActual('antd');
  return {
    ...actual,
    ConfigProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  };
});

// Mock MarketContext
vi.mock('../contexts/MarketContext', () => ({
  MarketProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useMarket: () => ({
    marketData: {},
    isConnected: true,
    subscribe: vi.fn(),
    unsubscribe: vi.fn(),
    prices: [], // 添加prices数组
    enabledSymbols: [], // 添加enabledSymbols数组
    subscribeToSymbol: vi.fn(),
    unsubscribeFromSymbol: vi.fn(),
  }),
}));

// 创建测试包装器
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <MarketProvider>
      {children}
    </MarketProvider>
  );
};

describe('App组件', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确渲染应用标题', () => {
    render(<TestWrapper><App /></TestWrapper>);
    expect(screen.getByText('诺亚量化')).toBeInTheDocument();
  });

  it('应该显示默认菜单项', () => {
    render(<TestWrapper><App /></TestWrapper>);

    expect(screen.getByText('仪表盘')).toBeInTheDocument();
    expect(screen.getByText('策略')).toBeInTheDocument();
    expect(screen.getByText('回测')).toBeInTheDocument();
    expect(screen.getByText('行情测试')).toBeInTheDocument();
    expect(screen.getByText('日志')).toBeInTheDocument();
    expect(screen.getByText('设置')).toBeInTheDocument();
  });

  it('应该在组件挂载时加载系统状态', async () => {
    const mockSystemStatus = {
      online: true,
      connection_status: 'connected',
      mode: 'paper',
      active_strategies: 2,
      total_strategies: 5,
    };

    (apiService.getSystemStatus as any).mockResolvedValue(mockSystemStatus);

    render(<TestWrapper><App /></TestWrapper>);

    await waitFor(() => {
      expect(apiService.getSystemStatus).toHaveBeenCalled();
    });
  });

  it('应该订阅交易更新', () => {
    render(<TestWrapper><App /></TestWrapper>);

    expect(wsService.subscribeToTrades).toHaveBeenCalled();
  });

  it('应该能够切换页面', () => {
    render(<TestWrapper><App /></TestWrapper>);

    // 点击策略菜单项
    fireEvent.click(screen.getByText('策略'));

    // 这里可以检查页面内容是否改变
    // 由于我们没有实际的页面组件，这个测试可能需要调整
  });

  it('应该显示连接状态', async () => {
    const mockSystemStatus = {
      online: true,
      connection_status: 'connected',
    };

    (apiService.getSystemStatus as any).mockResolvedValue(mockSystemStatus);

    render(<TestWrapper><App /></TestWrapper>);

    await waitFor(() => {
      // 检查连接状态是否显示
      expect(screen.getByText('ONLINE')).toBeInTheDocument();
    });
  });

  it('应该处理API错误', async () => {
    (apiService.getSystemStatus as any).mockRejectedValue(new Error('API错误'));

    // 模拟console.error以避免测试输出错误
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(<TestWrapper><App /></TestWrapper>);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Failed to load system status:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it('应该显示最新交易信息', async () => {
    const mockTrade = {
      symbol: 'BTCUSDT',
      side: 'BUY',
      price: 50000,
      timestamp: new Date().toISOString(),
    };

    let tradeCallback: (trade: any) => void;
    (wsService.subscribeToTrades as any).mockImplementation((callback: any) => {
      tradeCallback = callback;
      return () => {}; // 取消订阅函数
    });

    render(<TestWrapper><App /></TestWrapper>);

    // 模拟接收到交易数据
    if (tradeCallback!) {
      tradeCallback(mockTrade);
    }

    await waitFor(() => {
      expect(screen.getByText('BTCUSDT')).toBeInTheDocument();
      expect(screen.getByText('BUY')).toBeInTheDocument();
      expect(screen.getByText('$50000.00')).toBeInTheDocument();
    });
  });

  it('应该定期刷新系统状态', async () => {
    vi.useFakeTimers();

    const mockSystemStatus = {
      online: true,
      connection_status: 'connected',
    };

    (apiService.getSystemStatus as any).mockResolvedValue(mockSystemStatus);

    render(<TestWrapper><App /></TestWrapper>);

    // 快进10秒
    vi.advanceTimersByTime(10000);

    await waitFor(() => {
      // 应该调用了至少2次（初始加载 + 定时刷新）
      expect(apiService.getSystemStatus).toHaveBeenCalledTimes(2);
    });

    vi.useRealTimers();
  });
});
