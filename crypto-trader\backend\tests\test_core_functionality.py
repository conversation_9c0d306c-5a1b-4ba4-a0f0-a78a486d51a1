"""核心功能测试"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
import json

from app.core.exceptions import (
    TradingSystemException, 
    RiskControlException, 
    TradingException,
    APIConnectionException,
    DataValidationException,
    ConfigurationException,
    ErrorCodes
)


@pytest.mark.unit
class TestExceptionHandling:
    """异常处理测试"""
    
    def test_trading_system_exception_creation(self):
        """测试交易系统异常创建"""
        exception = TradingSystemException(
            message="测试异常",
            error_code="TEST_ERROR",
            details={"field": "test"}
        )
        
        assert exception.message == "测试异常"
        assert exception.error_code == "TEST_ERROR"
        assert exception.details == {"field": "test"}
    
    def test_risk_control_exception(self):
        """测试风控异常"""
        exception = RiskControlException(
            message="风控触发",
            risk_type="DAILY_LOSS",
            details={"current_loss": -1000}
        )
        
        assert exception.message == "风控触发"
        assert exception.error_code == "RISK_CONTROL_VIOLATION"
        assert exception.risk_type == "DAILY_LOSS"
    
    def test_trading_exception(self):
        """测试交易异常"""
        exception = TradingException(
            message="交易失败",
            symbol="BTCUSDT",
            order_id="12345"
        )
        
        assert exception.message == "交易失败"
        assert exception.symbol == "BTCUSDT"
        assert exception.order_id == "12345"
    
    def test_api_connection_exception(self):
        """测试API连接异常"""
        exception = APIConnectionException(
            message="连接失败",
            exchange="binance"
        )
        
        assert exception.message == "连接失败"
        assert exception.exchange == "binance"
    
    def test_data_validation_exception(self):
        """测试数据验证异常"""
        exception = DataValidationException(
            message="数据无效",
            field="price",
            value=-100
        )
        
        assert exception.message == "数据无效"
        assert exception.field == "price"
        assert exception.value == -100
    
    def test_configuration_exception(self):
        """测试配置异常"""
        exception = ConfigurationException(
            message="配置错误",
            config_key="api_key"
        )
        
        assert exception.message == "配置错误"
        assert exception.config_key == "api_key"


@pytest.mark.unit
class TestErrorCodes:
    """错误代码测试"""
    
    def test_error_codes_constants(self):
        """测试错误代码常量"""
        assert ErrorCodes.SYSTEM_ERROR == "SYSTEM_ERROR"
        assert ErrorCodes.RISK_CONTROL_VIOLATION == "RISK_CONTROL_VIOLATION"
        assert ErrorCodes.TRADING_ERROR == "TRADING_ERROR"
        assert ErrorCodes.API_CONNECTION_ERROR == "API_CONNECTION_ERROR"
        assert ErrorCodes.DATA_VALIDATION_ERROR == "DATA_VALIDATION_ERROR"
        assert ErrorCodes.CONFIGURATION_ERROR == "CONFIGURATION_ERROR"


@pytest.mark.unit
class TestDataValidation:
    """数据验证测试"""
    
    def test_symbol_validation(self):
        """测试交易对验证"""
        def validate_symbol(symbol: str) -> bool:
            if not symbol:
                return False
            if not symbol.endswith('USDT'):
                return False
            if len(symbol) < 6:  # 至少3个字符 + USDT
                return False
            return True
        
        assert validate_symbol("BTCUSDT") is True
        assert validate_symbol("ETHUSDT") is True
        assert validate_symbol("") is False
        assert validate_symbol("BTC") is False
        assert validate_symbol("BTCUSD") is False
    
    def test_price_validation(self):
        """测试价格验证"""
        def validate_price(price: float) -> bool:
            return price > 0
        
        assert validate_price(100.0) is True
        assert validate_price(0.001) is True
        assert validate_price(0) is False
        assert validate_price(-100) is False
    
    def test_quantity_validation(self):
        """测试数量验证"""
        def validate_quantity(quantity: float, min_qty: float = 0.001) -> bool:
            return quantity >= min_qty
        
        assert validate_quantity(1.0) is True
        assert validate_quantity(0.001) is True
        assert validate_quantity(0.0001, 0.001) is False
        assert validate_quantity(-1.0) is False
    
    def test_percentage_validation(self):
        """测试百分比验证"""
        def validate_percentage(pct: float) -> bool:
            return 0 <= pct <= 100
        
        assert validate_percentage(50.0) is True
        assert validate_percentage(0.0) is True
        assert validate_percentage(100.0) is True
        assert validate_percentage(-10.0) is False
        assert validate_percentage(150.0) is False


@pytest.mark.unit
class TestUtilityFunctions:
    """工具函数测试"""
    
    def test_format_price(self):
        """测试价格格式化"""
        def format_price(price: float, precision: int = 2) -> str:
            return f"{price:.{precision}f}"
        
        assert format_price(123.456) == "123.46"
        assert format_price(123.456, 4) == "123.4560"
        assert format_price(100) == "100.00"
    
    def test_calculate_percentage_change(self):
        """测试百分比变化计算"""
        def calculate_percentage_change(old_value: float, new_value: float) -> float:
            if old_value == 0:
                return 0.0
            return ((new_value - old_value) / old_value) * 100
        
        assert calculate_percentage_change(100, 110) == 10.0
        assert calculate_percentage_change(100, 90) == -10.0
        assert calculate_percentage_change(0, 100) == 0.0
    
    def test_calculate_pnl(self):
        """测试盈亏计算"""
        def calculate_pnl(entry_price: float, current_price: float, quantity: float, side: str) -> float:
            if side.upper() == "BUY":
                return (current_price - entry_price) * quantity
            else:  # SELL
                return (entry_price - current_price) * quantity
        
        # 做多盈利
        assert calculate_pnl(100, 110, 1.0, "BUY") == 10.0
        # 做多亏损
        assert calculate_pnl(100, 90, 1.0, "BUY") == -10.0
        # 做空盈利
        assert calculate_pnl(100, 90, 1.0, "SELL") == 10.0
        # 做空亏损
        assert calculate_pnl(100, 110, 1.0, "SELL") == -10.0
    
    def test_timestamp_conversion(self):
        """测试时间戳转换"""
        def timestamp_to_datetime(timestamp: int) -> datetime:
            return datetime.fromtimestamp(timestamp / 1000)
        
        def datetime_to_timestamp(dt: datetime) -> int:
            return int(dt.timestamp() * 1000)
        
        # 测试往返转换
        original_timestamp = *************  # 2022-01-01 00:00:00
        dt = timestamp_to_datetime(original_timestamp)
        converted_timestamp = datetime_to_timestamp(dt)
        
        assert abs(original_timestamp - converted_timestamp) < 1000  # 允许1秒误差


@pytest.mark.unit
class TestRiskCalculations:
    """风险计算测试"""
    
    def test_position_size_calculation(self):
        """测试仓位大小计算"""
        def calculate_position_size(
            account_balance: float, 
            risk_percentage: float, 
            entry_price: float, 
            stop_loss_price: float
        ) -> float:
            risk_amount = account_balance * (risk_percentage / 100)
            price_diff = abs(entry_price - stop_loss_price)
            if price_diff == 0:
                return 0
            return risk_amount / price_diff
        
        # 账户余额10000，风险2%，入场价100，止损价95
        position_size = calculate_position_size(10000, 2, 100, 95)
        assert position_size == 40.0  # 200 / 5 = 40
    
    def test_risk_reward_ratio(self):
        """测试风险回报比计算"""
        def calculate_risk_reward_ratio(
            entry_price: float, 
            stop_loss_price: float, 
            take_profit_price: float
        ) -> float:
            risk = abs(entry_price - stop_loss_price)
            reward = abs(take_profit_price - entry_price)
            if risk == 0:
                return 0
            return reward / risk
        
        # 入场100，止损95，止盈110
        ratio = calculate_risk_reward_ratio(100, 95, 110)
        assert ratio == 2.0  # 10 / 5 = 2
    
    def test_max_drawdown_calculation(self):
        """测试最大回撤计算"""
        def calculate_max_drawdown(equity_curve: list) -> float:
            if not equity_curve:
                return 0.0
            
            peak = equity_curve[0]
            max_dd = 0.0
            
            for value in equity_curve:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak
                max_dd = max(max_dd, drawdown)
            
            return max_dd * 100  # 返回百分比
        
        # 测试资金曲线
        equity = [10000, 11000, 10500, 12000, 9000, 13000]
        max_dd = calculate_max_drawdown(equity)
        assert abs(max_dd - 25.0) < 0.01  # 从12000跌到9000，回撤25%


@pytest.mark.integration
class TestSystemIntegration:
    """系统集成测试"""
    
    def test_error_response_format(self):
        """测试错误响应格式"""
        from app.core.exceptions import create_error_response
        
        response = create_error_response(
            error_code="TEST_ERROR",
            message="测试错误",
            status_code=400,
            details={"field": "test"}
        )
        
        assert response.status_code == 400
        content = json.loads(response.body)
        assert content["error"]["code"] == "TEST_ERROR"
        assert content["error"]["message"] == "测试错误"
        assert content["error"]["details"]["field"] == "test"
    
    def test_configuration_loading(self):
        """测试配置加载"""
        # 模拟配置加载
        config = {
            "debug": True,
            "database_url": "sqlite:///test.db",
            "redis_url": "redis://localhost:6379",
            "secret_key": "test_secret"
        }
        
        assert config["debug"] is True
        assert "sqlite" in config["database_url"]
        assert config["secret_key"] == "test_secret"
