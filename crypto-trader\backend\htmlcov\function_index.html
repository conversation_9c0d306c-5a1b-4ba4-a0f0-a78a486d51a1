<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">33%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-16 00:31 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd___init___py.html">app\api\v1\__init__.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t16">app\api\v1\account.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t16"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t24">app\api\v1\account.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t24"><data value='positions'>positions</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t81">app\api\v1\account.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t81"><data value='account_summary'>account_summary</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t203">app\api\v1\account.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t203"><data value='get_equity_data'>get_equity_data</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t238">app\api\v1\account.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t238"><data value='close_all_positions'>close_all_positions</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t257">app\api\v1\account.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html#t257"><data value='sync_account_data'>sync_account_data</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html">app\api\v1\account.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t33">app\api\v1\backtest.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t33"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t42">app\api\v1\backtest.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t42"><data value='run_backtest'>run_backtest</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t117">app\api\v1\backtest.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t117"><data value='optimize_and_save'>optimize_and_save</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t204">app\api\v1\backtest.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t204"><data value='full_backtest'>full_backtest</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t321">app\api\v1\backtest.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t321"><data value='upsert_strategy'>upsert_strategy</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t410">app\api\v1\backtest.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html#t410"><data value='walkforward_backtest'>walkforward_backtest</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html">app\api\v1\backtest.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_logs_py.html#t8">app\api\v1\logs.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_logs_py.html#t8"><data value='recent_logs'>recent_logs</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_logs_py.html">app\api\v1\logs.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_logs_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t26">app\api\v1\market.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t26"><data value='websocket_endpoint'>websocket_endpoint</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t42">app\api\v1\market.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t42"><data value='get_websocket_status'>get_websocket_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t47">app\api\v1\market.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t47"><data value='get_market_data'>get_market_data</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t67">app\api\v1\market.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t67"><data value='get_enabled_symbols'>get_enabled_symbols</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t80">app\api\v1\market.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t80"><data value='get_symbol_ticker'>get_symbol_ticker</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t110">app\api\v1\market.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html#t110"><data value='get_exchange_symbols'>get_exchange_symbols</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html">app\api\v1\market.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="28 31">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t15">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t15"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t30">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t30"><data value='get_settings'>get_settings</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t76">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t76"><data value='set_binance_credentials'>set_binance_credentials</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t148">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t148"><data value='set_trading_mode'>set_trading_mode</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t219">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t219"><data value='delete_binance_credentials'>delete_binance_credentials</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t248">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t248"><data value='test_connection'>test_connection</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t313">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t313"><data value='get_risk_limits'>get_risk_limits</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t344">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t344"><data value='set_risk_limits'>set_risk_limits</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t372">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t372"><data value='test_risk_controls'>test_risk_controls</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t400">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t400"><data value='reset_risk_controls'>reset_risk_controls</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t12">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t12"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t20">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t20"><data value='get_latest_signals'>get_latest_signals</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t69">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t69"><data value='create_signal'>create_signal</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t106">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t106"><data value='get_signals_by_symbol'>get_signals_by_symbol</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t127">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t127"><data value='get_signal_stats'>get_signal_stats</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t163">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t163"><data value='get_current_signals'>get_current_signals</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t187">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t187"><data value='update_signal_status'>update_signal_status</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t243">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t243"><data value='get_signal_history'>get_signal_history</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t271">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html#t271"><data value='clear_signal_status'>clear_signal_status</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t21">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t21"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t30">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t30"><data value='list_strategies'>list_strategies</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t54">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t54"><data value='create_strategy'>create_strategy</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t111">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t111"><data value='update_strategy'>update_strategy</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t154">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t154"><data value='start_strategy'>start_strategy</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t167">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t167"><data value='stop_strategy'>stop_strategy</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t179">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t179"><data value='strategy_status'>strategy_status</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t190">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t190"><data value='running_strategies'>running_strategies</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t195">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t195"><data value='activate_all_strategies'>activate_all_strategies</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t223">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t223"><data value='deactivate_all_strategies'>deactivate_all_strategies</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t251">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t251"><data value='delete_strategy'>delete_strategy</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t270">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html#t270"><data value='get_strategies_status'>get_strategies_status</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t31">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t31"><data value='get_api_credentials'>_get_api_credentials</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t48">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t48"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t56">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t56"><data value='get_system_status'>get_system_status</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t96">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t96"><data value='trigger_risk_alert'>trigger_risk_alert</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t126">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t126"><data value='clear_risk_alert'>clear_risk_alert</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t147">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t147"><data value='set_system_mode'>set_system_mode</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t192">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t192"><data value='get_system_latency'>get_system_latency</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t208">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t208"><data value='test_binance_connection'>test_binance_connection</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t260">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t260"><data value='get_account_info'>get_account_info</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t302">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t302"><data value='get_time_sync_status'>get_time_sync_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t307">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t307"><data value='force_time_sync'>force_time_sync</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t329">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t329"><data value='get_websocket_status'>get_websocket_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t334">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t334"><data value='test_paper_order'>test_paper_order</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t362">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t362"><data value='close_all_positions'>close_all_positions</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t371">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t371"><data value='get_symbol_info'>get_symbol_info</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t396">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t396"><data value='get_detailed_health'>get_detailed_health</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t441">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t441"><data value='get_comprehensive_health'>get_comprehensive_health</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t495">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t495"><data value='get_system_metrics'>get_system_metrics</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t532">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html#t532"><data value='check_specific_service'>check_specific_service</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>59</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="57 59">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html#t60">app\core\backtest_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html#t60"><data value='load_price_series'>load_price_series</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html#t106">app\core\backtest_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html#t106"><data value='compute_ma_strategy'>compute_ma_strategy</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html#t213">app\core\backtest_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html#t213"><data value='performance_metrics'>performance_metrics</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html#t257">app\core\backtest_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html#t257"><data value='main'>main</data></a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html">app\core\backtest_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="18 19">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html#t8">app\core\binance.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html#t8"><data value='init__'>BinanceClient.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html#t13">app\core\binance.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html#t13"><data value='ping'>BinanceClient.ping</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html#t18">app\core\binance.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html#t18"><data value='kline'>BinanceClient.kline</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html">app\core\binance.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t38">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t38"><data value='get_settings'>get_settings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t16">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t16"><data value='init__'>TradingSystemException.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t25">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t25"><data value='init__'>RiskControlException.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t32">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t32"><data value='init__'>TradingException.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t40">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t40"><data value='init__'>APIConnectionException.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t47">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t47"><data value='init__'>DataValidationException.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t55">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t55"><data value='init__'>ConfigurationException.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t60">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t60"><data value='create_error_response'>create_error_response</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t86">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t86"><data value='trading_system_exception_handler'>trading_system_exception_handler</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t118">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t118"><data value='http_exception_handler'>http_exception_handler</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t138">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t138"><data value='validation_exception_handler'>validation_exception_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t158">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t158"><data value='general_exception_handler'>general_exception_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>46</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="46 46">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html#t12">app\core\log_store.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html#t12"><data value='emit'>InMemoryHandler.emit</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html#t32">app\core\log_store.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html#t32"><data value='get_recent'>get_recent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html">app\core\log_store.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t10">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t10"><data value='init__'>LogBroadcaster.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t14">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t14"><data value='add'>LogBroadcaster.add</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t18">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t18"><data value='remove'>LogBroadcaster.remove</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t22">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t22"><data value='broadcast'>LogBroadcaster.broadcast</data></a></td>
                <td>9</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="4 9">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t45">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t45"><data value='init__'>WSLogHandler.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t49">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t49"><data value='should_send'>WSLogHandler._should_send</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t64">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t64"><data value='emit'>WSLogHandler.emit</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t15">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t15"><data value='configure_logging'>configure_logging</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t58">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t58"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t65">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t65"><data value='init__'>TradingLogger.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t68">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t68"><data value='log_signal'>TradingLogger.log_signal</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t79">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t79"><data value='log_order'>TradingLogger.log_order</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t92">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t92"><data value='log_trade'>TradingLogger.log_trade</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t105">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t105"><data value='log_position_update'>TradingLogger.log_position_update</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t116">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t116"><data value='log_risk_event'>TradingLogger.log_risk_event</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t126">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t126"><data value='log_system_event'>TradingLogger.log_system_event</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t136">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t136"><data value='log_api_call'>TradingLogger.log_api_call</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t149">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t149"><data value='log_error'>TradingLogger.log_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t162">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t162"><data value='init__'>PerformanceLogger.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t165">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t165"><data value='log_latency'>PerformanceLogger.log_latency</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t175">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t175"><data value='log_throughput'>PerformanceLogger.log_throughput</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t188">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t188"><data value='log_resource_usage'>PerformanceLogger.log_resource_usage</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t18">app\core\market_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t18"><data value='init__'>MarketBroadcaster.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t22">app\core\market_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t22"><data value='add'>MarketBroadcaster.add</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t27">app\core\market_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t27"><data value='remove'>MarketBroadcaster.remove</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t32">app\core\market_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t32"><data value='broadcast'>MarketBroadcaster.broadcast</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t43">app\core\market_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t43"><data value='stream_binance'>stream_binance</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html">app\core\market_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t15">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t15"><data value='dispatch'>RequestIDMiddleware.dispatch</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t30">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t30"><data value='dispatch'>LoggingMiddleware.dispatch</data></a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t89">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t89"><data value='dispatch'>SecurityHeadersMiddleware.dispatch</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t110">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t110"><data value='init__'>RateLimitMiddleware.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t116">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t116"><data value='dispatch'>RateLimitMiddleware.dispatch</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t157">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t157"><data value='init__'>CORSMiddleware.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t162">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t162"><data value='dispatch'>CORSMiddleware.dispatch</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t188">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t188"><data value='dispatch'>HealthCheckMiddleware.dispatch</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t24">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t24"><data value='post_init__'>HealthCheckResult.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t40">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t40"><data value='post_init__'>SystemMetrics.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t48">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t48"><data value='init__'>HealthChecker.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t52">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t52"><data value='register_check'>HealthChecker.register_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t59">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t59"><data value='run_check'>HealthChecker.run_check</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t115">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t115"><data value='run_all_checks'>HealthChecker.run_all_checks</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t141">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t141"><data value='get_overall_status'>HealthChecker.get_overall_status</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t159">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t159"><data value='init__'>SystemMonitor.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t164">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t164"><data value='get_system_metrics'>SystemMonitor.get_system_metrics</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t220">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t220"><data value='get_metrics_summary'>SystemMonitor.get_metrics_summary</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t267">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t267"><data value='database_health_check'>database_health_check</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t290">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t290"><data value='redis_health_check'>redis_health_check</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t320">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t320"><data value='binance_api_health_check'>binance_api_health_check</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>46</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="3 46">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t18">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t18"><data value='init__'>APIKeyManager.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t22">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t22"><data value='get_or_create_master_key'>APIKeyManager._get_or_create_master_key</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t43">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t43"><data value='encrypt_api_key'>APIKeyManager.encrypt_api_key</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t60">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t60"><data value='decrypt_api_key'>APIKeyManager.decrypt_api_key</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t69">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t69"><data value='generate_key_id'>APIKeyManager._generate_key_id</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t75">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t75"><data value='verify_key_id'>APIKeyManager.verify_key_id</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t80">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t80"><data value='mask_api_key'>APIKeyManager.mask_api_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t89">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t89"><data value='init__'>VaultManager.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t92">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t92"><data value='store_credentials'>VaultManager.store_credentials</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t110">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t110"><data value='retrieve_credentials'>VaultManager.retrieve_credentials</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t119">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t119"><data value='validate_credentials_format'>VaultManager.validate_credentials_format</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t10">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t10"><data value='init__'>SignalBroadcaster.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t13">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t13"><data value='add'>SignalBroadcaster.add</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t18">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t18"><data value='remove'>SignalBroadcaster.remove</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t23">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t23"><data value='broadcast'>SignalBroadcaster.broadcast</data></a></td>
                <td>11</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="6 11">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t41">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t41"><data value='broadcast_signal'>SignalBroadcaster.broadcast_signal</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t59">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t59"><data value='broadcast_signal_legacy'>SignalBroadcaster.broadcast_signal_legacy</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t79">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t79"><data value='init__'>TradeBroadcaster.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t82">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t82"><data value='add'>TradeBroadcaster.add</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t87">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t87"><data value='remove'>TradeBroadcaster.remove</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t92">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t92"><data value='broadcast_trade'>TradeBroadcaster.broadcast_trade</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t30">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t30"><data value='init__'>StrategyRunner.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t39">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t39"><data value='init_history'>StrategyRunner._init_history</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t63">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t63"><data value='run_loop'>StrategyRunner.run_loop</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t121">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t121"><data value='start'>StrategyRunner.start</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t127">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t127"><data value='stop'>StrategyRunner.stop</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t137">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t137"><data value='init__'>StrategyManager.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t140">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t140"><data value='start_strategy'>StrategyManager.start_strategy</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t147">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t147"><data value='stop_strategy'>StrategyManager.stop_strategy</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_base_py.html">app\db\base.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>108</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="108 108">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_session_py.html#t9">app\db\session.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_session_py.html#t9"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_session_py.html">app\db\session.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_session_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t44">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t44"><data value='lifespan'>lifespan</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t182">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t182"><data value='ping'>ping</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t186">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t186"><data value='health'>health</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t198">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t198"><data value='ws_market'>ws_market</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t209">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t209"><data value='ws_logs'>ws_logs</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t220">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t220"><data value='ws_signal'>ws_signal</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t231">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t231"><data value='ws_trade'>ws_trade</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>69</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="68 69">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html">app\schemas\strategy.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t19">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t19"><data value='init__'>BinanceClientService.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t23">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t23"><data value='get_client'>BinanceClientService.get_client</data></a></td>
                <td>16</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="10 16">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t58">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t58"><data value='test_connection'>BinanceClientService.test_connection</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t87">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t87"><data value='get_account_info'>BinanceClientService.get_account_info</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t109">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t109"><data value='calc_margin'>BinanceClientService._calc_margin</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t124">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t124"><data value='calc_margin_rate'>BinanceClientService._calc_margin_rate</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t135">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t135"><data value='get_positions'>BinanceClientService.get_positions</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t167">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t167"><data value='create_order'>BinanceClientService.create_order</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t210">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t210"><data value='cancel_order'>BinanceClientService.cancel_order</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t224">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t224"><data value='cancel_all_orders'>BinanceClientService.cancel_all_orders</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t238">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t238"><data value='get_open_orders'>BinanceClientService.get_open_orders</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t255">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t255"><data value='get_order_history'>BinanceClientService.get_order_history</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t268">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t268"><data value='change_leverage'>BinanceClientService.change_leverage</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t282">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t282"><data value='get_symbol_info'>BinanceClientService.get_symbol_info</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t299">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t299"><data value='calculate_quantity'>BinanceClientService.calculate_quantity</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t328">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t328"><data value='close_client'>BinanceClientService.close_client</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t337">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t337"><data value='close_all_clients'>BinanceClientService.close_all_clients</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t344">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t344"><data value='get_24hr_ticker'>BinanceClientService.get_24hr_ticker</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t369">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t369"><data value='get_exchange_info'>BinanceClientService.get_exchange_info</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t19">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t19"><data value='init__'>ExchangeInfoService.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t25">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t25"><data value='get_exchange_info'>ExchangeInfoService.get_exchange_info</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t37">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t37"><data value='get_symbol_info'>ExchangeInfoService.get_symbol_info</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t48">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t48"><data value='need_update'>ExchangeInfoService._need_update</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t56">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t56"><data value='update_exchange_info'>ExchangeInfoService._update_exchange_info</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t114">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t114"><data value='format_price'>ExchangeInfoService.format_price</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t129">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t129"><data value='format_quantity'>ExchangeInfoService.format_quantity</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t144">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t144"><data value='validate_order'>ExchangeInfoService.validate_order</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t184">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t184"><data value='get_leverage_brackets'>ExchangeInfoService.get_leverage_brackets</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t197">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t197"><data value='get_max_leverage'>ExchangeInfoService.get_max_leverage</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t211">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t211"><data value='init__'>LeverageManager.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t214">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t214"><data value='set_leverage'>LeverageManager.set_leverage</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t235">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t235"><data value='get_leverage'>LeverageManager.get_leverage</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t254">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t254"><data value='auto_adjust_leverage'>LeverageManager.auto_adjust_leverage</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t21">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t21"><data value='init__'>MarketWebSocketManager.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t28">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t28"><data value='connect'>MarketWebSocketManager.connect</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t38">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t38"><data value='disconnect'>MarketWebSocketManager.disconnect</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t47">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t47"><data value='start_market_push'>MarketWebSocketManager.start_market_push</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t56">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t56"><data value='stop_market_push'>MarketWebSocketManager.stop_market_push</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t67">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t67"><data value='market_push_loop'>MarketWebSocketManager._market_push_loop</data></a></td>
                <td>15</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="10 15">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t93">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t93"><data value='get_enabled_symbols'>MarketWebSocketManager._get_enabled_symbols</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t110">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t110"><data value='broadcast_market_data'>MarketWebSocketManager._broadcast_market_data</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t141">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t141"><data value='send_to_client'>MarketWebSocketManager._send_to_client</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t151">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t151"><data value='send_message'>MarketWebSocketManager.send_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t155">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t155"><data value='start'>MarketWebSocketManager.start</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t161">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t161"><data value='stop'>MarketWebSocketManager.stop</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t167">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t167"><data value='get_status'>MarketWebSocketManager.get_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t18">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t18"><data value='init__'>PriceService.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t23">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t23"><data value='init_redis'>PriceService.init_redis</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t33">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t33"><data value='get_current_price'>PriceService.get_current_price</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t63">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t63"><data value='get_multiple_prices'>PriceService.get_multiple_prices</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t88">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t88"><data value='batch_ticker'>PriceService.batch_ticker</data></a></td>
                <td>15</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="4 15">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t142">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t142"><data value='get_cached_price'>PriceService._get_cached_price</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t158">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t158"><data value='cache_price'>PriceService._cache_price</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t174">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t174"><data value='fetch_price_from_binance'>PriceService._fetch_price_from_binance</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t197">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t197"><data value='get_24h_ticker'>PriceService.get_24h_ticker</data></a></td>
                <td>14</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="7 14">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t229">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t229"><data value='close'>PriceService.close</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t23">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t23"><data value='init__'>RateLimiter.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t31">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t31"><data value='can_make_request'>RateLimiter.can_make_request</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t51">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t51"><data value='record_request'>RateLimiter.record_request</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t58">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t58"><data value='wait_if_needed'>RateLimiter.wait_if_needed</data></a></td>
                <td>4</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="1 4">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t68">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t68"><data value='init__'>CacheManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t78">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t78"><data value='init_redis'>CacheManager.init_redis</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t88">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t88"><data value='get_cache_key'>CacheManager._get_cache_key</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t92">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t92"><data value='get'>CacheManager.get</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t117">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t117"><data value='set'>CacheManager.set</data></a></td>
                <td>12</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="7 12">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t141">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t141"><data value='delete'>CacheManager.delete</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t159">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t159"><data value='init__'>RetryManager.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t165">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t165"><data value='execute_with_retry'>RetryManager.execute_with_retry</data></a></td>
                <td>27</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="7 27">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t230">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t230"><data value='with_cache'>with_cache</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t232">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t232"><data value='decorator'>with_cache.decorator</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t234">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t234"><data value='wrapper'>with_cache.decorator.wrapper</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t260">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t260"><data value='with_retry'>with_retry</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t262">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t262"><data value='decorator'>with_retry.decorator</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t264">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t264"><data value='wrapper'>with_retry.decorator.wrapper</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t19">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t19"><data value='init__'>SignalService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t23">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t23"><data value='start'>SignalService.start</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t34">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t34"><data value='stop'>SignalService.stop</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t39">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t39"><data value='signal_update_loop'>SignalService._signal_update_loop</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t49">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t49"><data value='update_signals'>SignalService._update_signals</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t75">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t75"><data value='generate_signal_for_symbol'>SignalService._generate_signal_for_symbol</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t113">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t113"><data value='simulate_strategy_analysis'>SignalService._simulate_strategy_analysis</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t134">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t134"><data value='broadcast_signal_update'>SignalService._broadcast_signal_update</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t155">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t155"><data value='manual_update_signal'>SignalService.manual_update_signal</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t18">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t18"><data value='init__'>TimeSyncService.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t27">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t27"><data value='start_sync_monitor'>TimeSyncService.start_sync_monitor</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t39">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t39"><data value='sync_time'>TimeSyncService.sync_time</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t86">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t86"><data value='get_server_time'>TimeSyncService.get_server_time</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t95">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t95"><data value='is_time_sync_valid'>TimeSyncService.is_time_sync_valid</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t108">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t108"><data value='can_place_order'>TimeSyncService.can_place_order</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t118">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t118"><data value='get_sync_status'>TimeSyncService.get_sync_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t130">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t130"><data value='force_sync'>TimeSyncService.force_sync</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t143">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t143"><data value='init__'>TimeValidator.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t146">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t146"><data value='validate_order_timestamp'>TimeValidator.validate_order_timestamp</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t165">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t165"><data value='get_valid_timestamp'>TimeValidator.get_valid_timestamp</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t25">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t25"><data value='init__'>TradeService.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t32">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t32"><data value='get_trading_mode'>TradeService.get_trading_mode</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t61">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t61"><data value='get_api_credentials'>TradeService.get_api_credentials</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t91">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t91"><data value='check_risk_limits'>TradeService.check_risk_limits</data></a></td>
                <td>16</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="8 16">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t120">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t120"><data value='handle_signal'>TradeService.handle_signal</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t157">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t157"><data value='calculate_position_size'>TradeService.calculate_position_size</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t244">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t244"><data value='execute_paper_trade'>TradeService.execute_paper_trade</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t305">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t305"><data value='execute_real_trade'>TradeService.execute_real_trade</data></a></td>
                <td>88</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="49 88">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t488">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t488"><data value='update_position'>TradeService.update_position</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t531">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t531"><data value='sync_account_info'>TradeService.sync_account_info</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t569">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t569"><data value='close_all_positions'>TradeService.close_all_positions</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t29">app\services\websocket_manager.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t29"><data value='init__'>WebSocketManager.__init__</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t64">app\services\websocket_manager.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t64"><data value='connect'>WebSocketManager.connect</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t95">app\services\websocket_manager.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t95"><data value='disconnect'>WebSocketManager.disconnect</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t113">app\services\websocket_manager.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t113"><data value='set_status'>WebSocketManager._set_status</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t123">app\services\websocket_manager.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t123"><data value='is_connected'>WebSocketManager.is_connected</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t131">app\services\websocket_manager.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t131"><data value='get_connection_info'>WebSocketManager.get_connection_info</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html">app\services\websocket_manager.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html#t55">app\utils\optimize.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html#t55"><data value='search_best_ma'>search_best_ma</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html#t93">app\utils\optimize.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html#t93"><data value='try_params'>search_best_ma._try_params</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html#t172">app\utils\optimize.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html#t172"><data value='validate_csv_format'>validate_csv_format</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html#t203">app\utils\optimize.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html#t203"><data value='prepare_dataframe'>prepare_dataframe</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html">app\utils\optimize.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="12 34">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t70">app\utils\walkforward.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t70"><data value='optimize_params_optuna'>optimize_params_optuna</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t85">app\utils\walkforward.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t85"><data value='objective'>optimize_params_optuna.objective</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t125">app\utils\walkforward.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t125"><data value='optimize_params_grid'>optimize_params_grid</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t216">app\utils\walkforward.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t216"><data value='walk_forward_single'>walk_forward_single</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t300">app\utils\walkforward.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t300"><data value='walkforward_optimize'>walkforward_optimize</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t362">app\utils\walkforward.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html#t362"><data value='validate_walkforward_data'>validate_walkforward_data</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html">app\utils\walkforward.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="23 47">49%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>3891</td>
                <td>2619</td>
                <td>3</td>
                <td class="right" data-ratio="1272 3891">33%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-16 00:31 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
