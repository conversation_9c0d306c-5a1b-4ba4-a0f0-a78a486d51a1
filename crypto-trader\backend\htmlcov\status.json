{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "5d89648c266b5113484ec8f96a561d46", "files": {"z_ae9440dee34b72cd___init___py": {"hash": "df506f115d4db8cc2045d087001fac1e", "index": {"url": "z_ae9440dee34b72cd___init___py.html", "file": "app\\api\\v1\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae9440dee34b72cd_account_py": {"hash": "0643771544b14db97f218fa1123c6a85", "index": {"url": "z_ae9440dee34b72cd_account_py.html", "file": "app\\api\\v1\\account.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 116, "n_excluded": 0, "n_missing": 93, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae9440dee34b72cd_backtest_py": {"hash": "999c4c4973ec55859dac578fe8840393", "index": {"url": "z_ae9440dee34b72cd_backtest_py.html", "file": "app\\api\\v1\\backtest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 174, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae9440dee34b72cd_logs_py": {"hash": "b146f1939e2b8f170c55048b3b918722", "index": {"url": "z_ae9440dee34b72cd_logs_py.html", "file": "app\\api\\v1\\logs.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae9440dee34b72cd_market_py": {"hash": "2b2c2586f6acc2252f5657a5a9b685de", "index": {"url": "z_ae9440dee34b72cd_market_py.html", "file": "app\\api\\v1\\market.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae9440dee34b72cd_settings_py": {"hash": "8e5bbb9c9780442605184389662859e5", "index": {"url": "z_ae9440dee34b72cd_settings_py.html", "file": "app\\api\\v1\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 215, "n_excluded": 0, "n_missing": 180, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae9440dee34b72cd_signals_py": {"hash": "dbfae2e47e54498d9b8877631b3a3a08", "index": {"url": "z_ae9440dee34b72cd_signals_py.html", "file": "app\\api\\v1\\signals.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 104, "n_excluded": 0, "n_missing": 79, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae9440dee34b72cd_strategy_py": {"hash": "79b63ddbea5fb4d9d916bd6e3c90e4d7", "index": {"url": "z_ae9440dee34b72cd_strategy_py.html", "file": "app\\api\\v1\\strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 137, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae9440dee34b72cd_system_py": {"hash": "9485d95b0354817e3d3664c30a636c5e", "index": {"url": "z_ae9440dee34b72cd_system_py.html", "file": "app\\api\\v1\\system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 264, "n_excluded": 0, "n_missing": 207, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_backtest_engine_py": {"hash": "229a6458f1579ea284897ab4296375a9", "index": {"url": "z_adebc8a9b0574ea2_backtest_engine_py.html", "file": "app\\core\\backtest_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_binance_py": {"hash": "58036cb561026fa84c482d2f01c22a8d", "index": {"url": "z_adebc8a9b0574ea2_binance_py.html", "file": "app\\core\\binance.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_config_py": {"hash": "7e243efb88c75b69b0710e9ba8603266", "index": {"url": "z_adebc8a9b0574ea2_config_py.html", "file": "app\\core\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 3, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_exceptions_py": {"hash": "d04b3159c7a6cce16089cefb9bc04775", "index": {"url": "z_adebc8a9b0574ea2_exceptions_py.html", "file": "app\\core\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_log_store_py": {"hash": "37960d2ce4379d03d0714f47823e4924", "index": {"url": "z_adebc8a9b0574ea2_log_store_py.html", "file": "app\\core\\log_store.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_log_ws_py": {"hash": "2d4285e2a8b3e6c5ce5476ecbf99df7d", "index": {"url": "z_adebc8a9b0574ea2_log_ws_py.html", "file": "app\\core\\log_ws.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 49, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_logging_config_py": {"hash": "5dd2af665599c36e97e1a226d5bda237", "index": {"url": "z_adebc8a9b0574ea2_logging_config_py.html", "file": "app\\core\\logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_market_ws_py": {"hash": "4519968a2e2f7a786cb9b5c03f832434", "index": {"url": "z_adebc8a9b0574ea2_market_ws_py.html", "file": "app\\core\\market_ws.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_middleware_py": {"hash": "58d51f45ba122e15221d2433293e27de", "index": {"url": "z_adebc8a9b0574ea2_middleware_py.html", "file": "app\\core\\middleware.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_monitoring_py": {"hash": "7ff1cfb7aa3816af96ef261a6342ece5", "index": {"url": "z_adebc8a9b0574ea2_monitoring_py.html", "file": "app\\core\\monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 138, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_security_py": {"hash": "a9e958ff339e9fc6597d25f98229870a", "index": {"url": "z_adebc8a9b0574ea2_security_py.html", "file": "app\\core\\security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 36, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_signal_ws_py": {"hash": "269ab762ced248fb00ac7220bdb41969", "index": {"url": "z_adebc8a9b0574ea2_signal_ws_py.html", "file": "app\\core\\signal_ws.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 60, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_strategy_engine_py": {"hash": "94c00e612be955d1decb7b966998233f", "index": {"url": "z_adebc8a9b0574ea2_strategy_engine_py.html", "file": "app\\core\\strategy_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_311acb9c3d04c524_base_py": {"hash": "e281aa8bdc4ee5b765d579a2d2a87373", "index": {"url": "z_311acb9c3d04c524_base_py.html", "file": "app\\db\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_311acb9c3d04c524_models_py": {"hash": "a0512a5f3e2f470b034f5732a081f5d3", "index": {"url": "z_311acb9c3d04c524_models_py.html", "file": "app\\db\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 108, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_311acb9c3d04c524_session_py": {"hash": "7a0386aca929d2fd1cae44750323f3c0", "index": {"url": "z_311acb9c3d04c524_session_py.html", "file": "app\\db\\session.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "ee61510a1fef696b8cd89a87d3a27348", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174_strategy_py": {"hash": "e1225dcdf0af152d649ee9f162ffb933", "index": {"url": "z_b4c115836e286174_strategy_py.html", "file": "app\\schemas\\strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70___init___py": {"hash": "0975189a62fff15291ae550e9c26060c", "index": {"url": "z_4c37ce8615b5aa70___init___py.html", "file": "app\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_binance_client_py": {"hash": "c113fd521b0a99da1313054a090527db", "index": {"url": "z_4c37ce8615b5aa70_binance_client_py.html", "file": "app\\services\\binance_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 207, "n_excluded": 0, "n_missing": 166, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_exchange_info_py": {"hash": "69ae28228bf9f115fc1f50ab28a42d8b", "index": {"url": "z_4c37ce8615b5aa70_exchange_info_py.html", "file": "app\\services\\exchange_info.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 155, "n_excluded": 0, "n_missing": 122, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_market_websocket_py": {"hash": "2b8a6c8af5baf484b7562b6a7374d233", "index": {"url": "z_4c37ce8615b5aa70_market_websocket_py.html", "file": "app\\services\\market_websocket.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 51, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_price_service_py": {"hash": "a5a6781f15c7c22bc9f24583c54773da", "index": {"url": "z_4c37ce8615b5aa70_price_service_py.html", "file": "app\\services\\price_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_rate_limiter_py": {"hash": "289e0af58e2ab8f6d5ef876df5feddf2", "index": {"url": "z_4c37ce8615b5aa70_rate_limiter_py.html", "file": "app\\services\\rate_limiter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 148, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_signal_service_py": {"hash": "2cfbdcc1fb38d859d1a2c595c0748af6", "index": {"url": "z_4c37ce8615b5aa70_signal_service_py.html", "file": "app\\services\\signal_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 97, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_time_sync_py": {"hash": "766829755f5422afbd20c30681758b8b", "index": {"url": "z_4c37ce8615b5aa70_time_sync_py.html", "file": "app\\services\\time_sync.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 67, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_trade_service_py": {"hash": "ac3bc46fe1745cd63865e00033fdbfe0", "index": {"url": "z_4c37ce8615b5aa70_trade_service_py.html", "file": "app\\services\\trade_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 317, "n_excluded": 0, "n_missing": 231, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_websocket_manager_py": {"hash": "f1ee98d10c47afd15ebab4ae1a5238ca", "index": {"url": "z_4c37ce8615b5aa70_websocket_manager_py.html", "file": "app\\services\\websocket_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_optimize_py": {"hash": "dee46b7f97c7c94f3416644f779b2466", "index": {"url": "z_a7b07432402c05f1_optimize_py.html", "file": "app\\utils\\optimize.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 91, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_walkforward_py": {"hash": "91f00ee49c46f8ad31d53df40a344137", "index": {"url": "z_a7b07432402c05f1_walkforward_py.html", "file": "app\\utils\\walkforward.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 0, "n_missing": 150, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}