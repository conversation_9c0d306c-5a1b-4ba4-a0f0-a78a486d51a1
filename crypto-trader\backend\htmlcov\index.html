<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">33%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-16 00:31 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd___init___py.html">app\api\v1\__init__.py</a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html">app\api\v1\account.py</a></td>
                <td>116</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="23 116">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html">app\api\v1\backtest.py</a></td>
                <td>174</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="28 174">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_logs_py.html">app\api\v1\logs.py</a></td>
                <td>11</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="6 11">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html">app\api\v1\market.py</a></td>
                <td>88</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="28 88">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html">app\api\v1\settings.py</a></td>
                <td>215</td>
                <td>180</td>
                <td>0</td>
                <td class="right" data-ratio="35 215">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html">app\api\v1\signals.py</a></td>
                <td>104</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="25 104">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html">app\api\v1\strategy.py</a></td>
                <td>137</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="32 137">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html">app\api\v1\system.py</a></td>
                <td>264</td>
                <td>207</td>
                <td>0</td>
                <td class="right" data-ratio="57 264">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html">app\core\backtest_engine.py</a></td>
                <td>145</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="18 145">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html">app\core\binance.py</a></td>
                <td>16</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="6 16">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td>19</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html">app\core\exceptions.py</a></td>
                <td>76</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="68 76">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html">app\core\log_store.py</a></td>
                <td>15</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="7 15">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html">app\core\log_ws.py</a></td>
                <td>49</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="37 49">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html">app\core\logging_config.py</a></td>
                <td>50</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="37 50">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html">app\core\market_ws.py</a></td>
                <td>57</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="24 57">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html">app\core\middleware.py</a></td>
                <td>82</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="43 82">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html">app\core\monitoring.py</a></td>
                <td>141</td>
                <td>138</td>
                <td>0</td>
                <td class="right" data-ratio="3 141">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html">app\core\security.py</a></td>
                <td>68</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="32 68">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html">app\core\signal_ws.py</a></td>
                <td>60</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="31 60">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html">app\core\strategy_engine.py</a></td>
                <td>87</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="25 87">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_base_py.html">app\db\base.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html">app\db\models.py</a></td>
                <td>108</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="108 108">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_session_py.html">app\db\session.py</a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app\main.py</a></td>
                <td>139</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="93 139">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html">app\schemas\strategy.py</a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html">app\services\binance_client.py</a></td>
                <td>207</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="41 207">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html">app\services\exchange_info.py</a></td>
                <td>155</td>
                <td>122</td>
                <td>0</td>
                <td class="right" data-ratio="33 155">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html">app\services\market_websocket.py</a></td>
                <td>106</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="55 106">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html">app\services\price_service.py</a></td>
                <td>115</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="33 115">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html">app\services\rate_limiter.py</a></td>
                <td>148</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="84 148">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html">app\services\signal_service.py</a></td>
                <td>97</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="0 97">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html">app\services\time_sync.py</a></td>
                <td>97</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="30 97">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html">app\services\trade_service.py</a></td>
                <td>317</td>
                <td>231</td>
                <td>0</td>
                <td class="right" data-ratio="86 317">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html">app\services\websocket_manager.py</a></td>
                <td>86</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="24 86">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html">app\utils\optimize.py</a></td>
                <td>103</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="12 103">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html">app\utils\walkforward.py</a></td>
                <td>173</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="23 173">13%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>3891</td>
                <td>2619</td>
                <td>3</td>
                <td class="right" data-ratio="1272 3891">33%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-16 00:31 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_a7b07432402c05f1_walkforward_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_ae9440dee34b72cd___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
