import React, { useState } from 'react';
import { 
  Card, Form, Button, Upload, InputNumber, message, Row, Col, Input, 
  Divider, Image, Descriptions, Collapse, Modal, Checkbox
} from 'antd';
import { Line } from '@ant-design/plots';
import { fullBacktest, upsertStrategy } from '../../api';

interface BacktestMetrics {
  [key: string]: string;
}

interface BacktestParams {
    fast: number;
    slow: number;
    thr: number;
    atr_len: number;
    atr_stop: number;
  vol_target: number;
  fixed_nominal: boolean;
}

interface EquityCurvePoint {
  time: string;
  equity: number;
}



// 指标中英对照
const METRIC_MAP: Record<string, string> = {
  'Total Return': '总收益率',
  'Annualized Return': '年化收益率',
  'Monthly Return': '月化收益率',
  'Max Drawdown': '最大回撤',
  'Sharpe Ratio': '夏普比率',
  'Fee Cost': '手续费消耗',
  'Final Equity': '期末权益',
};

const Backtest = () => {
  const [loading, setLoading] = useState(false);
  const [img, setImg] = useState<string>('');
  const [metrics, setMetrics] = useState<BacktestMetrics>({});
  const [params, setParams] = useState<BacktestParams | null>(null);
  const [equityCurve, setEquityCurve] = useState<EquityCurvePoint[]>([]);
  const [currentSymbol, setCurrentSymbol] = useState<string>('');
  const [imgPreviewOpen, setImgPreviewOpen] = useState(false);

  // 图表配置生成函数（Line + Area 背景）
  const getCurveConfig = (curve: EquityCurvePoint[]) => {
    return {
      data: curve,
      xField: 'time',
      yField: 'equity',
      height: 340,
      autoFit: true,
      smooth: true,
      lineStyle: { stroke: '#4facfe', lineWidth: 2 },
      area: {
        style: { fill: 'l(270) 0:#4facfe 1:#00f2fe', opacity: 0.2 },
      },
      color: '#4facfe',
      xAxis: { type: 'time', label: { autoHide: true } },
      yAxis: { label: { formatter: (v: any) => Number(v).toLocaleString() } },
      tooltip: { formatter: (d: any) => ({ name: '权益', value: d.equity.toFixed(2) }) },
      slider: { start: 0, end: 1, height: 12 },
      theme: 'dark' as any,
      interactions: [{ type: 'brush-x' }],
    } as any;
  };

  // 完整回测处理函数
  const handleFullBacktest = async (values: any) => {
    setLoading(true);
    setImg('');
    setMetrics({});
    setParams(null);
    setEquityCurve([]);

    try {
      const form = new FormData();
      
      // 文件处理
      if (values.file && values.file.length > 0) {
        form.append('file', values.file[0].originFileObj);
      } else {
        message.error('请选择一个CSV文件');
        setLoading(false);
        return;
      }
      
      form.append('symbol', values.symbol);

      // 复利开关处理: 未勾选即固定本金 (fixed_nominal = true)
      values.fixed_nominal = !values.enable_compound;
      
      // 可选参数 - 如果用户填写了就传递，否则后端使用默认最佳参数
      ['fast', 'slow', 'thr', 'atr_len', 'atr_stop', 'vol_target', 'fixed_nominal'].forEach(key => {
        if (values[key] != null) {
          form.append(key, String(values[key]));
        }
      });

      const result = await fullBacktest(form);
      
      setParams(result.params_used);
      setMetrics(result.metrics || {});
      setEquityCurve(result.equity_curve || []);
      setImg(result.image);
      setCurrentSymbol(values.symbol);
      
      message.success('完整回测执行成功');
    } catch (err: any) {
      const msg =
        err.response?.data?.detail ||
        (err.response?.data ? JSON.stringify(err.response.data) : null) ||
        err.message;
      message.error(`完整回测失败: ${msg}`);
      console.error('--- 完整回测错误 ---', err.response);
    } finally {
      setLoading(false);
    }
  };

  // 保存策略
  const handleSaveStrategy = async () => {
    if (!params || !currentSymbol) {
      message.warning('请先完成回测');
      return;
    }

    try {
      const form = new FormData();
      Object.entries(params).forEach(([k, v]) => form.append(k, String(v)));
      form.append('symbol', currentSymbol.toUpperCase());

      const result = await upsertStrategy(form);
      
      if (result.exists) {
        Modal.confirm({
          title: '策略已存在，是否覆盖？',
          content: `交易对 ${currentSymbol} 的策略已存在，覆盖将更新所有参数。`,
          okText: '覆盖',
          cancelText: '取消',
          onOk: async () => {
            form.append('force', 'true');
            try {
              await upsertStrategy(form);
              message.success('策略已覆盖');
            } catch (error: any) {
              message.error(`覆盖策略失败: ${error.response?.data?.detail || error.message}`);
            }
          }
        });
      } else {
        message.success('策略已创建');
      }
    } catch (error: any) {
      message.error(`保存策略失败: ${error.response?.data?.detail || error.message}`);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card 
        title="完整回测" 
        style={{ background: '#1f1f1f', color: '#fff' }}
      >
        <Form onFinish={handleFullBacktest} layout="vertical">
                  <Row gutter={16}>
            <Col span={8}>
                      <Form.Item 
                        name="file" 
                label="CSV文件" 
                        rules={[{ required: true, message: '请选择CSV文件' }]}
                valuePropName="fileList"
                getValueFromEvent={e => (Array.isArray(e) ? e : e && e.fileList)}
                      > 
                        <Upload beforeUpload={() => false} maxCount={1}>
                          <Button>选择CSV文件</Button>
                        </Upload>
                      </Form.Item>
                    </Col>
            <Col span={8}>
                      <Form.Item 
                        name="symbol" 
                label="交易对" 
                rules={[{ required: true, message: '请输入交易对' }]}
                      >
                <Input placeholder="如: BTCUSDT" />
                      </Form.Item>
                    </Col>
                  </Row>

          {/* 复利开关 */}
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="enable_compound"
                label="是否复利"
                valuePropName="checked"
                initialValue={false}
              >
                <Checkbox>启用复利</Checkbox>
              </Form.Item>
            </Col>
          </Row>

          <Collapse
            ghost
            items={[{
              key: '1',
              label: '高级参数设置 (可选 - 留空将使用最佳默认参数)',
              children: (
                <>
                  <Row gutter={16}>
                    <Col span={6}>
                      <Form.Item name="fast" label="快速MA周期">
                        <InputNumber min={1} max={100} placeholder="默认: 19" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item name="slow" label="慢速MA周期">
                        <InputNumber min={1} max={200} placeholder="默认: 44" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item name="thr" label="距离阈值">
                        <InputNumber min={0} max={0.01} step={0.00001} placeholder="默认: 4.095e-5" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item name="atr_len" label="ATR长度">
                        <InputNumber min={1} max={100} placeholder="默认: 42" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={6}>
                      <Form.Item name="atr_stop" label="ATR止损倍数">
                        <InputNumber min={0} max={10} step={0.1} placeholder="默认: 1.29928" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item name="vol_target" label="目标波动率">
                        <InputNumber min={0} max={1} step={0.01} placeholder="默认: 0.248" />
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              ),
            }]}
          />
                  
          <Form.Item style={{ marginTop: 16 }}>
            <Button type="primary" htmlType="submit" loading={loading} size="large">
              开始完整回测
                    </Button>
                  </Form.Item>
                </Form>

        {/* 回测结果展示 */}
        {params && (
          <div style={{ marginTop: 24 }}>
            <Divider orientation="left">回测结果</Divider>
            <Row gutter={16}>
              <Col span={12}>
                <Card title="使用的参数" size="small">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="快速MA">{params.fast}</Descriptions.Item>
                    <Descriptions.Item label="慢速MA">{params.slow}</Descriptions.Item>
                    <Descriptions.Item label="距离阈值">{params.thr}</Descriptions.Item>
                    <Descriptions.Item label="ATR长度">{params.atr_len}</Descriptions.Item>
                    <Descriptions.Item label="ATR止损">{params.atr_stop}</Descriptions.Item>
                    <Descriptions.Item label="目标波动率">{params.vol_target}</Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
              <Col span={12}>
                {metrics && Object.keys(metrics).length > 0 && (
                  <Card title="绩效指标" size="small">
                    <Descriptions column={1} size="small">
                      {Object.entries(metrics).map(([k, v]) => (
                        <Descriptions.Item
                          label={`${k}${METRIC_MAP[k] ? ' / ' + METRIC_MAP[k] : ''}`}
                          key={k}
                        >
                          {v}
                        </Descriptions.Item>
                      ))}
                    </Descriptions>
                  </Card>
                )}
              </Col>
            </Row>

            {/* 权益曲线图表 */}
            {equityCurve.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Card title="权益曲线" size="small">
                  {/* @ts-ignore */}
                  <Line {...getCurveConfig(equityCurve)} />
                </Card>
              </div>
            )}

            {/* 静态图表 (缩略图 + 点击放大) */}
            {img && (
              <div style={{ marginTop: 16 }}>
                <Card title="回测静态图" size="small" extra={<Button type="link" onClick={() => setImgPreviewOpen(true)}>查看大图</Button>}>
                  <Image 
                    src={img} 
                    height={180}
                    preview={false} 
                    style={{ objectFit: 'contain', width: '100%' }}
                  />
                </Card>
                <Modal
                  open={imgPreviewOpen}
                  footer={null}
                  onCancel={() => setImgPreviewOpen(false)}
                  width={800}
                >
                  <Image src={img} width="100%" />
                </Modal>
              </div>
            )}

            {/* 操作按钮 */}
            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Button 
                type="primary" 
                size="large"
                onClick={handleSaveStrategy}
              >
                保存为策略
                    </Button>
            </div>
                  </div>
                )}
              </Card>
    </div>
  );
};

export default Backtest;
