<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">33%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-16 00:31 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd___init___py.html">app\api\v1\__init__.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html">app\api\v1\account.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_account_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>116</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="23 116">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html">app\api\v1\backtest.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_backtest_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>174</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="28 174">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_logs_py.html">app\api\v1\logs.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_logs_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="6 11">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html">app\api\v1\market.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_market_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>88</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="28 88">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t22">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t22"><data value='BinanceCredentials'>BinanceCredentials</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t26">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html#t26"><data value='TradingMode'>TradingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html">app\api\v1\settings.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>215</td>
                <td>180</td>
                <td>0</td>
                <td class="right" data-ratio="35 215">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html">app\api\v1\signals.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_signals_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="25 104">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html">app\api\v1\strategy.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_strategy_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>137</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="32 137">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html">app\api\v1\system.py</a></td>
                <td class="name left"><a href="z_ae9440dee34b72cd_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>264</td>
                <td>207</td>
                <td>0</td>
                <td class="right" data-ratio="57 264">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html">app\core\backtest_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_backtest_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>145</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="18 145">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html#t7">app\core\binance.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html#t7"><data value='BinanceClient'>BinanceClient</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html">app\core\binance.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_binance_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t7">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t7"><data value='Settings'>Settings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t32">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t32"><data value='Config'>Settings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t14">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t14"><data value='TradingSystemException'>TradingSystemException</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t23">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t23"><data value='RiskControlException'>RiskControlException</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t30">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t30"><data value='TradingException'>TradingException</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t38">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t38"><data value='APIConnectionException'>APIConnectionException</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t45">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t45"><data value='DataValidationException'>DataValidationException</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t53">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t53"><data value='ConfigurationException'>ConfigurationException</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t180">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t180"><data value='ErrorCodes'>ErrorCodes</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="52 60">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html#t9">app\core\log_store.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html#t9"><data value='InMemoryHandler'>InMemoryHandler</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html">app\core\log_store.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_store_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t9">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t9"><data value='LogBroadcaster'>LogBroadcaster</data></a></td>
                <td>15</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="8 15">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t44">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html#t44"><data value='WSLogHandler'>WSLogHandler</data></a></td>
                <td>20</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="15 20">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html">app\core\log_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_log_ws_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t62">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t62"><data value='TradingLogger'>TradingLogger</data></a></td>
                <td>9</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="1 9">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t159">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html#t159"><data value='PerformanceLogger'>PerformanceLogger</data></a></td>
                <td>5</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="1 5">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html">app\core\logging_config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="35 36">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t15">app\core\market_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html#t15"><data value='MarketBroadcaster'>MarketBroadcaster</data></a></td>
                <td>16</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="10 16">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html">app\core\market_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_market_ws_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="14 41">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t12">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t12"><data value='RequestIDMiddleware'>RequestIDMiddleware</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t27">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t27"><data value='LoggingMiddleware'>LoggingMiddleware</data></a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t86">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t86"><data value='SecurityHeadersMiddleware'>SecurityHeadersMiddleware</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t107">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t107"><data value='RateLimitMiddleware'>RateLimitMiddleware</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t154">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t154"><data value='CORSMiddleware'>CORSMiddleware</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t185">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html#t185"><data value='HealthCheckMiddleware'>HealthCheckMiddleware</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html">app\core\middleware.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_middleware_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t15">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t15"><data value='HealthCheckResult'>HealthCheckResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t30">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t30"><data value='SystemMetrics'>SystemMetrics</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t45">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t45"><data value='HealthChecker'>HealthChecker</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t156">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html#t156"><data value='SystemMonitor'>SystemMonitor</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html">app\core\monitoring.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_monitoring_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>68</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="3 68">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t15">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t15"><data value='APIKeyManager'>APIKeyManager</data></a></td>
                <td>29</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="9 29">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t86">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html#t86"><data value='VaultManager'>VaultManager</data></a></td>
                <td>17</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="1 17">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html">app\core\security.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t9">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t9"><data value='SignalBroadcaster'>SignalBroadcaster</data></a></td>
                <td>24</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="9 24">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t78">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html#t78"><data value='TradeBroadcaster'>TradeBroadcaster</data></a></td>
                <td>17</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="3 17">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html">app\core\signal_ws.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_signal_ws_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t29">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t29"><data value='StrategyRunner'>StrategyRunner</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t134">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html#t134"><data value='StrategyManager'>StrategyManager</data></a></td>
                <td>10</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="1 10">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html">app\core\strategy_engine.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_strategy_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_base_py.html">app\db\base.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t8">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t8"><data value='StrategyParam'>StrategyParam</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t19">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t19"><data value='Order'>Order</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t31">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t31"><data value='Position'>Position</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t41">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t41"><data value='Log'>Log</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t51">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t51"><data value='Setting'>Setting</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t57">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t57"><data value='APICredential'>APICredential</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t84">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t84"><data value='Trade'>Trade</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t97">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t97"><data value='Signal'>Signal</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t108">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t108"><data value='SignalStatus'>SignalStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t119">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t119"><data value='AccountMetric'>AccountMetric</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t131">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t131"><data value='EquityRecord'>EquityRecord</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t139">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html#t139"><data value='SystemStatus'>SystemStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html">app\db\models.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>108</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="108 108">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_311acb9c3d04c524_session_py.html">app\db\session.py</a></td>
                <td class="name left"><a href="z_311acb9c3d04c524_session_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>139</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="93 139">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t6">app\schemas\strategy.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t6"><data value='StrategyBase'>StrategyBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t15">app\schemas\strategy.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t15"><data value='StrategyCreate'>StrategyCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t20">app\schemas\strategy.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t20"><data value='StrategyUpdate'>StrategyUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t29">app\schemas\strategy.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t29"><data value='StrategyRead'>StrategyRead</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t34">app\schemas\strategy.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t34"><data value='Config'>StrategyRead.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t38">app\schemas\strategy.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t38"><data value='StrategyResponse'>StrategyResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t45">app\schemas\strategy.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t45"><data value='StrategyListResponse'>StrategyListResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t53">app\schemas\strategy.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html#t53"><data value='StrategyStatusResponse'>StrategyStatusResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html">app\schemas\strategy.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_strategy_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t16">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html#t16"><data value='BinanceClientService'>BinanceClientService</data></a></td>
                <td>178</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="12 178">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html">app\services\binance_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_binance_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t16">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t16"><data value='ExchangeInfoService'>ExchangeInfoService</data></a></td>
                <td>94</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="4 94">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t208">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html#t208"><data value='LeverageManager'>LeverageManager</data></a></td>
                <td>33</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="1 33">3%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html">app\services\exchange_info.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_exchange_info_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t18">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html#t18"><data value='MarketWebSocketManager'>MarketWebSocketManager</data></a></td>
                <td>81</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="30 81">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html">app\services\market_websocket.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_market_websocket_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t15">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html#t15"><data value='PriceService'>PriceService</data></a></td>
                <td>96</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="14 96">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html">app\services\price_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_price_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t20">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t20"><data value='RateLimiter'>RateLimiter</data></a></td>
                <td>18</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="14 18">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t65">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t65"><data value='CacheManager'>CacheManager</data></a></td>
                <td>46</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="17 46">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t156">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html#t156"><data value='RetryManager'>RetryManager</data></a></td>
                <td>31</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="11 31">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html">app\services\rate_limiter.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_rate_limiter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="42 53">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t16">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html#t16"><data value='SignalService'>SignalService</data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html">app\services\signal_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_signal_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t15">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t15"><data value='TimeSyncService'>TimeSyncService</data></a></td>
                <td>60</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="7 60">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t140">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html#t140"><data value='TimeValidator'>TimeValidator</data></a></td>
                <td>15</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="1 15">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html">app\services\time_sync.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_time_sync_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t22">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html#t22"><data value='TradeService'>TradeService</data></a></td>
                <td>289</td>
                <td>231</td>
                <td>0</td>
                <td class="right" data-ratio="58 289">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html">app\services\trade_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_trade_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t18">app\services\websocket_manager.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t18"><data value='ConnectionStatus'>ConnectionStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t26">app\services\websocket_manager.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html#t26"><data value='WebSocketManager'>WebSocketManager</data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html">app\services\websocket_manager.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_websocket_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html">app\utils\optimize.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_optimize_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>103</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="12 103">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html">app\utils\walkforward.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_walkforward_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>173</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="23 173">13%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>3891</td>
                <td>2619</td>
                <td>3</td>
                <td class="right" data-ratio="1272 3891">33%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-16 00:31 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
