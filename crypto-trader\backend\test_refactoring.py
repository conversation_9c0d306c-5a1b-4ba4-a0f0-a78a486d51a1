#!/usr/bin/env python3
"""
重构验证测试 - 验证短期优化效果
测试新的服务类是否正常工作
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.constants import (
    TradingConstants, RiskConstants, SystemConstants,
    get_default_price, get_min_quantity, validate_trading_mode
)
from app.core.exceptions import TradingSystemException, RiskControlException
from app.core.decorators import trading_exception_handler, is_valid_symbol
from app.services.risk_control_service import risk_control_service
from app.services.position_calculator_service import position_calculator_service
from app.services.credential_service import credential_service
from app.services.position_manager_service import position_manager_service


class RefactoringTests:
    """重构验证测试类"""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.total = 0
    
    def test(self, name: str, test_func):
        """执行单个测试"""
        self.total += 1
        try:
            print(f"🧪 测试 {self.total}: {name}")
            test_func()
            print(f"✅ 通过")
            self.passed += 1
        except Exception as e:
            print(f"❌ 失败: {e}")
            self.failed += 1
        print()
    
    async def async_test(self, name: str, test_func):
        """执行异步测试"""
        self.total += 1
        try:
            print(f"🧪 异步测试 {self.total}: {name}")
            await test_func()
            print(f"✅ 通过")
            self.passed += 1
        except Exception as e:
            print(f"❌ 失败: {e}")
            self.failed += 1
        print()
    
    def test_constants_module(self):
        """测试常量模块"""
        # 测试默认价格
        btc_price = get_default_price("BTCUSDT")
        assert btc_price > 0, "BTC默认价格应该大于0"
        
        # 测试最小数量
        min_qty = get_min_quantity("BTCUSDT")
        assert min_qty > 0, "最小数量应该大于0"
        
        # 测试交易模式验证
        assert validate_trading_mode("paper"), "paper应该是有效的交易模式"
        assert validate_trading_mode("testnet"), "testnet应该是有效的交易模式"
        assert validate_trading_mode("live"), "live应该是有效的交易模式"
        assert not validate_trading_mode("invalid"), "invalid不应该是有效的交易模式"
        
        # 测试常量访问
        assert TradingConstants.DEFAULT_COMMISSION_RATE > 0, "默认手续费率应该大于0"
        assert len(RiskConstants.DEFAULT_RISK_LIMITS) > 0, "应该有默认风控限制"
        assert SystemConstants.BAR_INTERVAL_SECONDS == 900, "K线间隔应该是900秒"
    
    def test_exception_handling(self):
        """测试异常处理"""
        # 测试自定义异常
        try:
            raise RiskControlException("测试风控异常", "TEST_RISK")
        except RiskControlException as e:
            assert e.error_code == "RISK_CONTROL_VIOLATION", "错误代码应该正确"
            assert e.risk_type == "TEST_RISK", "风险类型应该正确"
        
        # 测试装饰器
        @trading_exception_handler
        def test_decorated_function():
            raise ValueError("测试异常")
        
        try:
            test_decorated_function()
            assert False, "应该抛出异常"
        except TradingSystemException as e:
            assert "测试异常" in e.message, "异常消息应该包含原始错误"
    
    def test_validators(self):
        """测试验证器"""
        # 测试交易对验证
        assert is_valid_symbol("BTCUSDT"), "BTCUSDT应该是有效的交易对"
        assert is_valid_symbol("ETHUSDT"), "ETHUSDT应该是有效的交易对"
        assert not is_valid_symbol("INVALID"), "INVALID不应该是有效的交易对"
        assert not is_valid_symbol("BTC"), "BTC不应该是有效的交易对"
    
    async def test_risk_control_service(self):
        """测试风控服务"""
        # 测试服务初始化
        assert risk_control_service is not None, "风控服务应该已初始化"
        
        # 测试获取当前限制
        limits = risk_control_service.get_current_limits()
        assert isinstance(limits, dict), "限制应该是字典类型"
        assert 'max_daily_loss' in limits, "应该包含日亏损限制"
        
        # 测试更新限制
        old_limits = risk_control_service.get_current_limits()
        new_limits = {'max_daily_loss': -2000}
        risk_control_service.update_risk_limits(new_limits)
        updated_limits = risk_control_service.get_current_limits()
        assert updated_limits['max_daily_loss'] == -2000, "限制应该已更新"
        
        # 恢复原始限制
        risk_control_service.update_risk_limits(old_limits)
    
    async def test_position_calculator_service(self):
        """测试仓位计算服务"""
        # 测试服务初始化
        assert position_calculator_service is not None, "仓位计算服务应该已初始化"
        
        # 测试价格获取（模拟）
        try:
            price = await position_calculator_service.get_current_price("BTCUSDT")
            assert price > 0, "价格应该大于0"
        except Exception:
            # 如果无法获取实时价格，应该返回默认价格
            pass
    
    async def test_credential_service(self):
        """测试凭证服务"""
        # 测试服务初始化
        assert credential_service is not None, "凭证服务应该已初始化"
        
        # 测试凭证验证
        valid = await credential_service.validate_credentials("test123456789", "secret123456789")
        assert valid, "有效的凭证应该通过验证"
        
        invalid = await credential_service.validate_credentials("", "")
        assert not invalid, "空凭证不应该通过验证"
        
        invalid2 = await credential_service.validate_credentials("short", "short")
        assert not invalid2, "过短的凭证不应该通过验证"
    
    async def test_position_manager_service(self):
        """测试持仓管理服务"""
        # 测试服务初始化
        assert position_manager_service is not None, "持仓管理服务应该已初始化"
        
        # 这里只测试服务是否正确初始化，实际的数据库操作需要数据库连接
        print("持仓管理服务初始化正常")
    
    def test_service_integration(self):
        """测试服务集成"""
        # 测试所有服务都已正确导入和初始化
        services = [
            risk_control_service,
            position_calculator_service,
            credential_service,
            position_manager_service
        ]
        
        for service in services:
            assert service is not None, f"服务 {type(service).__name__} 应该已初始化"
        
        print("所有服务集成测试通过")
    
    def print_summary(self):
        """打印测试总结"""
        print("=" * 50)
        print(f"📊 测试总结:")
        print(f"   总计: {self.total}")
        print(f"   通过: {self.passed}")
        print(f"   失败: {self.failed}")
        print(f"   成功率: {self.passed/self.total*100:.1f}%")
        
        if self.failed == 0:
            print("🎉 所有测试通过！重构成功！")
        else:
            print("⚠️  部分测试失败，需要检查")
        print("=" * 50)


async def main():
    """主测试函数"""
    print("🚀 开始重构验证测试")
    print("=" * 50)
    
    tests = RefactoringTests()
    
    # 同步测试
    tests.test("常量模块", tests.test_constants_module)
    tests.test("异常处理", tests.test_exception_handling)
    tests.test("验证器", tests.test_validators)
    tests.test("服务集成", tests.test_service_integration)
    
    # 异步测试
    await tests.async_test("风控服务", tests.test_risk_control_service)
    await tests.async_test("仓位计算服务", tests.test_position_calculator_service)
    await tests.async_test("凭证服务", tests.test_credential_service)
    await tests.async_test("持仓管理服务", tests.test_position_manager_service)
    
    # 打印总结
    tests.print_summary()
    
    return tests.failed == 0


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试执行异常: {e}")
        sys.exit(1)
