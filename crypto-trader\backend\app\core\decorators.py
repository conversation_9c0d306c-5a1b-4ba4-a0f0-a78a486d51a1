"""
统一装饰器 - 提供异常处理、日志记录等通用功能
"""
import asyncio
import logging
import functools
from typing import Any, Callable, Dict, Optional, Type, Union
from datetime import datetime

from app.core.exceptions import (
    TradingSystemException, 
    TradingException, 
    RiskControlException,
    APIConnectionException,
    DataValidationException,
    ConfigurationException
)

logger = logging.getLogger(__name__)


def handle_trading_exceptions(
    default_return: Any = None,
    log_level: str = "ERROR",
    reraise: bool = True,
    exception_mapping: Optional[Dict[Type[Exception], Type[TradingSystemException]]] = None
):
    """
    统一异常处理装饰器
    
    Args:
        default_return: 异常时的默认返回值
        log_level: 日志级别
        reraise: 是否重新抛出异常
        exception_mapping: 异常映射关系
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except TradingSystemException:
                # 已经是我们的自定义异常，直接重新抛出
                raise
            except Exception as e:
                # 记录异常
                log_func = getattr(logger, log_level.lower(), logger.error)
                log_func(f"函数 {func.__name__} 执行异常: {e}", extra={
                    "function": func.__name__,
                    "function_args": str(args)[:200],  # 限制长度
                    "function_kwargs": str(kwargs)[:200],
                    "exception_type": type(e).__name__,
                    "exception_message": str(e)
                })
                
                # 异常映射
                if exception_mapping and type(e) in exception_mapping:
                    mapped_exception = exception_mapping[type(e)]
                    raise mapped_exception(
                        message=f"{func.__name__} 执行失败: {e}",
                        details={
                            "original_exception": type(e).__name__,
                            "function": func.__name__
                        }
                    )
                
                if reraise:
                    # 包装为通用交易异常
                    raise TradingSystemException(
                        message=f"{func.__name__} 执行失败: {e}",
                        details={
                            "original_exception": type(e).__name__,
                            "function": func.__name__
                        }
                    )
                else:
                    return default_return
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except TradingSystemException:
                raise
            except Exception as e:
                log_func = getattr(logger, log_level.lower(), logger.error)
                log_func(f"函数 {func.__name__} 执行异常: {e}", extra={
                    "function": func.__name__,
                    "function_args": str(args)[:200],
                    "function_kwargs": str(kwargs)[:200],
                    "exception_type": type(e).__name__,
                    "exception_message": str(e)
                })
                
                if exception_mapping and type(e) in exception_mapping:
                    mapped_exception = exception_mapping[type(e)]
                    raise mapped_exception(
                        message=f"{func.__name__} 执行失败: {e}",
                        details={
                            "original_exception": type(e).__name__,
                            "function": func.__name__
                        }
                    )
                
                if reraise:
                    raise TradingSystemException(
                        message=f"{func.__name__} 执行失败: {e}",
                        details={
                            "original_exception": type(e).__name__,
                            "function": func.__name__
                        }
                    )
                else:
                    return default_return
        
        # 根据函数类型返回对应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def log_execution_time(log_level: str = "INFO", threshold_ms: float = 1000):
    """
    记录函数执行时间的装饰器
    
    Args:
        log_level: 日志级别
        threshold_ms: 超过此阈值才记录日志（毫秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = datetime.now()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                end_time = datetime.now()
                duration_ms = (end_time - start_time).total_seconds() * 1000
                
                if duration_ms >= threshold_ms:
                    log_func = getattr(logger, log_level.lower(), logger.info)
                    log_func(f"函数 {func.__name__} 执行耗时: {duration_ms:.2f}ms", extra={
                        "function": func.__name__,
                        "duration_ms": duration_ms,
                        "start_time": start_time.isoformat(),
                        "end_time": end_time.isoformat()
                    })
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = datetime.now()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = datetime.now()
                duration_ms = (end_time - start_time).total_seconds() * 1000
                
                if duration_ms >= threshold_ms:
                    log_func = getattr(logger, log_level.lower(), logger.info)
                    log_func(f"函数 {func.__name__} 执行耗时: {duration_ms:.2f}ms", extra={
                        "function": func.__name__,
                        "duration_ms": duration_ms,
                        "start_time": start_time.isoformat(),
                        "end_time": end_time.isoformat()
                    })
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def validate_parameters(**validators):
    """
    参数验证装饰器
    
    Args:
        **validators: 参数验证器字典，格式为 {参数名: 验证函数}
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 获取函数签名
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 验证参数
            for param_name, validator in validators.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    try:
                        if not validator(value):
                            raise DataValidationException(
                                message=f"参数 {param_name} 验证失败",
                                field=param_name,
                                value=value
                            )
                    except Exception as e:
                        if isinstance(e, DataValidationException):
                            raise
                        raise DataValidationException(
                            message=f"参数 {param_name} 验证异常: {e}",
                            field=param_name,
                            value=value
                        )
            
            return await func(*args, **kwargs)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            for param_name, validator in validators.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    try:
                        if not validator(value):
                            raise DataValidationException(
                                message=f"参数 {param_name} 验证失败",
                                field=param_name,
                                value=value
                            )
                    except Exception as e:
                        if isinstance(e, DataValidationException):
                            raise
                        raise DataValidationException(
                            message=f"参数 {param_name} 验证异常: {e}",
                            field=param_name,
                            value=value
                        )
            
            return func(*args, **kwargs)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# 常用的异常映射
COMMON_EXCEPTION_MAPPING = {
    ConnectionError: APIConnectionException,
    TimeoutError: APIConnectionException,
    ValueError: DataValidationException,
    KeyError: ConfigurationException,
    AttributeError: ConfigurationException,
}


# 预定义的装饰器
trading_exception_handler = handle_trading_exceptions(
    exception_mapping=COMMON_EXCEPTION_MAPPING,
    log_level="ERROR"
)

risk_exception_handler = handle_trading_exceptions(
    exception_mapping={
        **COMMON_EXCEPTION_MAPPING,
        ValueError: RiskControlException,
    },
    log_level="WARNING"
)

api_exception_handler = handle_trading_exceptions(
    exception_mapping={
        ConnectionError: APIConnectionException,
        TimeoutError: APIConnectionException,
        OSError: APIConnectionException,
    },
    log_level="ERROR"
)

performance_monitor = log_execution_time(threshold_ms=500)


# 参数验证器
def is_valid_symbol(symbol: str) -> bool:
    """验证交易对格式"""
    from app.core.constants import validate_symbol_format
    return validate_symbol_format(symbol)


def is_positive_number(value: Union[int, float]) -> bool:
    """验证正数"""
    try:
        return float(value) > 0
    except (ValueError, TypeError):
        return False


def is_valid_side(side: str) -> bool:
    """验证交易方向"""
    return side.upper() in ["BUY", "SELL"]


def is_valid_trading_mode(mode: str) -> bool:
    """验证交易模式"""
    from app.core.constants import validate_trading_mode
    return validate_trading_mode(mode)


# 导出
__all__ = [
    'handle_trading_exceptions',
    'log_execution_time', 
    'validate_parameters',
    'trading_exception_handler',
    'risk_exception_handler',
    'api_exception_handler',
    'performance_monitor',
    'is_valid_symbol',
    'is_positive_number',
    'is_valid_side',
    'is_valid_trading_mode',
]
